# 🤖 Trading Bot - RSI Strategie

Automatický trading bot pro futures obchodování s RSI strategií. Bot podporuje demo i live účty na Binance a Bybit burzách.

## 🚀 Funkce

- **RSI Strategie**: Nákup pod 30, prodej nad 70
- **Multi-asset trading**: Obchodování více aktiv současně
- **Risk Management**: Stop Loss (3%) a Take Profit (5%)
- **Position Sizing**: Maximálně 20% kapitálu na obchod
- **Demo účet**: Testování na testnet
- **Backtesting**: Testování na historických datech
- **24/7 provoz**: Kontinuální běh s error handling
- **Detailní logging**: Záznamy všech operací

## 📋 Požadavky

- Python 3.8+
- API klíče od Binance nebo Bybit
- Internetové připojení

## 🛠️ Instalace

1. **Klonování projektu**:
```bash
git clone <repository-url>
cd obchobot
```

2. **Instalace závislostí**:
```bash
pip install -r requirements.txt
```

3. **Konfigurace**:
```bash
cp .env.example .env
# Upravte .env soubor s vašimi API klíči
```

## ⚙️ Konfigurace

Upravte `.env` soubor:

```env
# API konfigurace
EXCHANGE=binance
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here
SANDBOX=True

# Trading parametry
SYMBOLS=BTC/USDT,ETH/USDT,ADA/USDT
TIMEFRAME=1h

# RSI strategie
RSI_PERIOD=14
RSI_OVERSOLD=30
RSI_OVERBOUGHT=70

# Risk management
MAX_POSITION_SIZE=0.2
STOP_LOSS_PERCENT=3.0
TAKE_PROFIT_PERCENT=5.0
```

## 🎯 Použití

### Test připojení
```bash
python main.py --mode test
```

### Backtesting
```bash
python main.py --mode backtest
```

### Live trading
```bash
python main.py --mode live
```

## 📊 Backtesting

Bot automaticky:
- Stáhne historická data
- Simuluje obchody podle RSI strategie
- Vypočítá metriky výkonnosti
- Vytvoří grafy a reporty
- Uloží výsledky do JSON souborů

### Metriky výkonnosti:
- **Total Return**: Celkový výnos v %
- **Win Rate**: Procento vítězných obchodů
- **Profit Factor**: Poměr zisků ku ztrátám
- **Sharpe Ratio**: Rizikově upravený výnos
- **Maximum Drawdown**: Největší pokles portfolia

## 🔧 Struktura projektu

```
obchobot/
├── main.py              # Hlavní spouštěcí soubor
├── bot.py               # Hlavní třída trading bota
├── strategy.py          # RSI strategie
├── risk_management.py   # Správa rizik
├── backtesting.py       # Backtesting engine
├── logger.py            # Logging systém
├── utils.py             # Pomocné funkce
├── config.py            # Konfigurace
├── requirements.txt     # Python závislosti
├── .env.example         # Příklad konfigurace
└── README.md           # Dokumentace
```

## 🛡️ Bezpečnost

### Demo účet (doporučeno pro začátečníky):
- Nastavte `SANDBOX=True` v `.env`
- Používejte testnet API klíče
- Žádné reálné peníze nejsou ohroženy

### Live trading:
- **POZOR**: Používejte pouze s penězi, které si můžete dovolit ztratit
- Začněte s malým kapitálem
- Důkladně otestujte strategii na demo účtu
- Sledujte bot pravidelně

## 📈 RSI Strategie

### Logika:
1. **Nákup**: RSI ≤ 30 (oversold)
2. **Prodej**: RSI ≥ 70 (overbought)
3. **Stop Loss**: 3% pod nákupní cenou
4. **Take Profit**: 5% nad nákupní cenou

### Risk Management:
- Maximálně 20% kapitálu na jeden obchod
- Automatické SL/TP ordery
- Sledování nerealizovaného P&L

## 🔍 Monitoring

Bot vytváří detailní logy:
- `logs/trading_bot.log` - Všechny operace
- Obchody s cenami a důvody
- Chyby a varování
- Stav portfolia

## 🚨 Upozornění

⚠️ **DŮLEŽITÉ**:
- Trading je rizikový - můžete ztratit peníze
- Bot je poskytován "jak je" bez záruky
- Vždy testujte na demo účtu
- Neobchodujte s penězi, které si nemůžete dovolit ztratit
- Sledujte bot pravidelně

## 🤝 Podpora

Pro otázky a podporu:
1. Zkontrolujte logy v `logs/trading_bot.log`
2. Ověřte konfiguraci v `.env`
3. Otestujte připojení: `python main.py --mode test`

## 📝 Licence

Tento projekt je poskytován pro vzdělávací účely. Používejte na vlastní riziko.

---

**Disclaimer**: Trading kryptoměn je vysoce rizikový. Tento bot je určen pouze pro vzdělávací účely. Autor nenese odpovědnost za finanční ztráty.
