#!/usr/bin/env python3
"""
Test Binance testnet API s va<PERSON><PERSON><PERSON>
"""

import ccxt
import sys
import os
from dotenv import load_dotenv

# Načtení environment variables
load_dotenv()

def test_binance_testnet():
    """Test Binance testnet API"""
    
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    print("🔍 Test Binance Testnet API...")
    print(f"API Key: {api_key[:16]}...")
    print(f"API Secret: {api_secret[:16]}...")
    
    # Různé konfigurace pro Binance testnet
    configs = [
        {
            'name': 'Základní testnet',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': True,
            }
        },
        {
            'name': 'Testnet s explicitním URL',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': True,
                'urls': {
                    'api': {
                        'public': 'https://testnet.binancefuture.com/fapi',
                        'private': 'https://testnet.binancefuture.com/fapi',
                    }
                }
            }
        },
        {
            'name': 'Testnet s hostname',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'testnet': True,
                'hostname': 'testnet.binancefuture.com',
            }
        },
        {
            'name': 'Futures testnet',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': True,
                'options': {
                    'defaultType': 'future',
                }
            }
        }
    ]
    
    for config_info in configs:
        print(f"\n🧪 Test: {config_info['name']}")
        try:
            exchange = ccxt.binance(config_info['config'])
            
            # Test 1: Získání balance
            print("   📊 Získávání balance...")
            balance = exchange.fetch_balance()
            
            print(f"   ✅ Balance získán!")
            print(f"   💰 USDT: {balance.get('USDT', {}).get('free', 0)}")
            
            # Výpis všech dostupných měn
            available_currencies = [k for k, v in balance.items() 
                                  if isinstance(v, dict) and v.get('free', 0) > 0]
            print(f"   💱 Dostupné měny: {available_currencies}")
            
            # Test 2: Získání ticker
            print("   📈 Získávání ticker...")
            ticker = exchange.fetch_ticker('BTC/USDT')
            print(f"   ✅ BTC/USDT: {ticker['last']}")
            
            # Test 3: Získání pozic (pro futures)
            try:
                print("   📋 Získávání pozic...")
                positions = exchange.fetch_positions()
                open_positions = [p for p in positions if p['contracts'] > 0]
                print(f"   ✅ Otevřené pozice: {len(open_positions)}")
            except Exception as e:
                print(f"   ⚠️  Pozice nedostupné: {e}")
            
            print(f"   🎉 {config_info['name']} - ÚSPĚCH!")
            return True, exchange
            
        except Exception as e:
            print(f"   ❌ {config_info['name']} - chyba: {e}")
    
    return False, None

def test_public_data():
    """Test veřejných dat bez autentifikace"""
    print("\n📊 Test veřejných dat...")
    
    try:
        # Test bez API klíčů
        exchange = ccxt.binance({'sandbox': True})
        
        # Test ticker
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ BTC/USDT ticker: {ticker['last']}")
        
        # Test orderbook
        orderbook = exchange.fetch_order_book('BTC/USDT', 5)
        print(f"✅ Orderbook: bid={orderbook['bids'][0][0]}, ask={orderbook['asks'][0][0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu veřejných dat: {e}")
        return False

def check_api_permissions():
    """Kontrola oprávnění API"""
    print("\n🔐 Doporučení pro Binance testnet API:")
    print("Ujistěte se, že máte nastavena tato oprávnění:")
    print("✅ Enable Reading")
    print("✅ Enable Futures")
    print("❌ Enable Withdrawals (NIKDY nepovolujte!)")
    print("\nTaké zkontrolujte:")
    print("- Že používáte testnet klíče z https://testnet.binancefuture.com/")
    print("- IP whitelist (pokud je nastaven)")
    print("- Expiraci API klíčů")

def main():
    """Hlavní funkce"""
    print("🤖 BINANCE TESTNET API TEST")
    print("=" * 50)
    
    # Test veřejných dat
    test_public_data()
    
    # Test s API klíči
    success, exchange = test_binance_testnet()
    
    if success:
        print("\n🎉 PŘIPOJENÍ ÚSPĚŠNÉ!")
        print("=" * 50)
        print("✅ VÝSLEDEK: Binance testnet API klíče fungují!")
        print("📋 Můžete nyní používat bota:")
        print("   1. python main.py --mode test")
        print("   2. python main.py --mode backtest") 
        print("   3. python main.py --mode live (testnet)")
        print("\n💡 INFORMACE:")
        print("   - Máte Binance TESTNET klíče")
        print("   - Bot bude používat demo peníze")
        print("   - Bezpečné pro testování")
        
    else:
        print("\n❌ PŘIPOJENÍ SELHALO")
        print("🔧 Možná řešení:")
        print("1. Zkontrolujte, že klíče jsou z https://testnet.binancefuture.com/")
        print("2. Ověřte oprávnění API klíčů")
        print("3. Zkontrolujte IP whitelist")
        print("4. Zkuste vytvořit nové API klíče")
        print("5. Použijte demo režim: python demo_mode.py")
        
        # Doporučení
        check_api_permissions()

if __name__ == "__main__":
    main()
