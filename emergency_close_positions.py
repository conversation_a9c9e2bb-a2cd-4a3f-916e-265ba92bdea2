#!/usr/bin/env python3
"""
🚨 EMERGENCY SCRIPT - UZAVŘENÍ VŠECH POZIC
Okamžité uzavření všech otevřených pozic pro zastavení ztrát
"""

import ccxt
import sys
from config import Config, BINANCE_TESTNET, BYBIT_TESTNET
from logger import logger
from colors import error, success, warning, highlight

def emergency_close_all_positions():
    """Uzavření všech otevřených pozic"""
    print("🚨 EMERGENCY: UZAVÍRÁNÍ VŠECH POZIC")
    print("=" * 50)
    
    try:
        # Inicializace burzy
        if Config.EXCHANGE.lower() == 'binance':
            if Config.SANDBOX:
                exchange = ccxt.binance(BINANCE_TESTNET)
            else:
                exchange = ccxt.binance({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        elif Config.EXCHANGE.lower() == 'bybit':
            if Config.SANDBOX:
                exchange = ccxt.bybit(BYBIT_TESTNET)
            else:
                exchange = ccxt.bybit({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        
        exchange.set_sandbox_mode(Config.SANDBOX)
        
        # Získání aktuálního zůstatku
        balance = exchange.fetch_balance()
        print(f"💰 Aktuální balance: {balance.get('USDT', {}).get('free', 0)} USDT")
        
        # Získání všech otevřených pozic
        positions = exchange.fetch_positions()
        open_positions = [pos for pos in positions if pos['contracts'] > 0]
        
        if not open_positions:
            print(success("✅ Žádné otevřené pozice k uzavření"))
            return True
        
        print(f"🔍 Nalezeno {len(open_positions)} otevřených pozic:")
        
        closed_positions = 0
        total_pnl = 0
        
        for position in open_positions:
            symbol = position['symbol']
            size = position['contracts']
            side = position['side']
            entry_price = position['entryPrice']
            mark_price = position['markPrice']
            pnl = position['unrealizedPnl']
            
            print(f"\n📊 {symbol}:")
            print(f"   Size: {size}")
            print(f"   Side: {side}")
            print(f"   Entry: {entry_price}")
            print(f"   Mark: {mark_price}")
            print(f"   PnL: {pnl:.2f} USDT")
            
            try:
                # Uzavření pozice market orderem
                if side == 'long':
                    order = exchange.create_market_order(symbol, 'sell', size)
                else:
                    order = exchange.create_market_order(symbol, 'buy', size)
                
                if order and order.get('status') == 'closed':
                    print(success(f"✅ {symbol} pozice uzavřena"))
                    closed_positions += 1
                    total_pnl += pnl
                else:
                    print(error(f"❌ Chyba uzavření {symbol}"))
                    
            except Exception as e:
                print(error(f"❌ Chyba při uzavírání {symbol}: {e}"))
        
        print(f"\n📊 SOUHRN:")
        print(f"✅ Uzavřeno pozic: {closed_positions}/{len(open_positions)}")
        print(f"💰 Celkový PnL: {total_pnl:.2f} USDT")
        
        # Finální balance
        final_balance = exchange.fetch_balance()
        final_usdt = final_balance.get('USDT', {}).get('free', 0)
        print(f"💰 Finální balance: {final_usdt} USDT")
        
        return closed_positions == len(open_positions)
        
    except Exception as e:
        print(error(f"❌ Kritická chyba: {e}"))
        return False

def emergency_close_specific_symbols():
    """Uzavření pozic pro konkrétní symboly"""
    symbols_to_close = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
    
    print("🚨 EMERGENCY: UZAVÍRÁNÍ KONKRÉTNÍCH SYMBOLŮ")
    print("=" * 50)
    print(f"Symboly: {', '.join(symbols_to_close)}")
    
    try:
        # Inicializace burzy
        if Config.EXCHANGE.lower() == 'binance':
            if Config.SANDBOX:
                exchange = ccxt.binance(BINANCE_TESTNET)
            else:
                exchange = ccxt.binance({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        elif Config.EXCHANGE.lower() == 'bybit':
            if Config.SANDBOX:
                exchange = ccxt.bybit(BYBIT_TESTNET)
            else:
                exchange = ccxt.bybit({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        
        exchange.set_sandbox_mode(Config.SANDBOX)
        
        closed_count = 0
        
        for symbol in symbols_to_close:
            try:
                # Získání aktuální pozice
                positions = exchange.fetch_positions([symbol])
                position = next((pos for pos in positions if pos['contracts'] > 0), None)
                
                if not position:
                    print(f"ℹ️ {symbol}: Žádná otevřená pozice")
                    continue
                
                size = position['contracts']
                side = position['side']
                pnl = position['unrealizedPnl']
                
                print(f"\n🔄 Uzavírání {symbol}:")
                print(f"   Size: {size}")
                print(f"   Side: {side}")
                print(f"   PnL: {pnl:.2f} USDT")
                
                # Uzavření pozice
                if side == 'long':
                    order = exchange.create_market_order(symbol, 'sell', size)
                else:
                    order = exchange.create_market_order(symbol, 'buy', size)
                
                if order and order.get('status') == 'closed':
                    print(success(f"✅ {symbol} úspěšně uzavřeno"))
                    closed_count += 1
                else:
                    print(error(f"❌ {symbol} se nepodařilo uzavřít"))
                    
            except Exception as e:
                print(error(f"❌ Chyba při uzavírání {symbol}: {e}"))
        
        print(f"\n📊 Uzavřeno {closed_count}/{len(symbols_to_close)} pozic")
        return closed_count > 0
        
    except Exception as e:
        print(error(f"❌ Kritická chyba: {e}"))
        return False

def show_current_positions():
    """Zobrazení aktuálních pozic"""
    print("📊 AKTUÁLNÍ POZICE")
    print("=" * 50)
    
    try:
        # Inicializace burzy
        if Config.EXCHANGE.lower() == 'binance':
            if Config.SANDBOX:
                exchange = ccxt.binance(BINANCE_TESTNET)
            else:
                exchange = ccxt.binance({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        elif Config.EXCHANGE.lower() == 'bybit':
            if Config.SANDBOX:
                exchange = ccxt.bybit(BYBIT_TESTNET)
            else:
                exchange = ccxt.bybit({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        
        exchange.set_sandbox_mode(Config.SANDBOX)
        
        # Balance
        balance = exchange.fetch_balance()
        usdt_balance = balance.get('USDT', {}).get('free', 0)
        print(f"💰 USDT Balance: {usdt_balance}")
        
        # Pozice
        positions = exchange.fetch_positions()
        open_positions = [pos for pos in positions if pos['contracts'] > 0]
        
        if not open_positions:
            print(success("✅ Žádné otevřené pozice"))
            return
        
        total_pnl = 0
        
        for position in open_positions:
            symbol = position['symbol']
            size = position['contracts']
            side = position['side']
            entry_price = position['entryPrice']
            mark_price = position['markPrice']
            pnl = position['unrealizedPnl']
            pnl_pct = position['percentage']
            
            total_pnl += pnl
            
            color_func = success if pnl > 0 else error if pnl < 0 else warning
            
            print(f"\n📊 {highlight(symbol)}:")
            print(f"   Size: {size}")
            print(f"   Side: {side}")
            print(f"   Entry: {entry_price}")
            print(f"   Mark: {mark_price}")
            print(f"   PnL: {color_func(f'{pnl:.2f} USDT ({pnl_pct:.2f}%)')}")
        
        print(f"\n💰 Celkový PnL: {success(f'{total_pnl:.2f} USDT') if total_pnl > 0 else error(f'{total_pnl:.2f} USDT')}")
        
    except Exception as e:
        print(error(f"❌ Chyba při získávání pozic: {e}"))

def main():
    """Hlavní funkce"""
    print("🚨 EMERGENCY POSITION MANAGER")
    print("=" * 50)
    
    while True:
        print(f"\nVyberte akci:")
        print(f"1. 📊 Zobrazit aktuální pozice")
        print(f"2. 🚨 Uzavřít VŠECHNY pozice")
        print(f"3. 🎯 Uzavřít konkrétní symboly (BTC/ETH/ADA)")
        print(f"4. ❌ Ukončit")
        
        choice = input(f"\nVaše volba (1-4): ").strip()
        
        if choice == '1':
            show_current_positions()
        elif choice == '2':
            confirm = input(warning("⚠️ Opravdu uzavřít VŠECHNY pozice? (ano/ne): ")).strip().lower()
            if confirm == 'ano':
                emergency_close_all_positions()
            else:
                print("Akce zrušena")
        elif choice == '3':
            confirm = input(warning("⚠️ Uzavřít BTC/ETH/ADA pozice? (ano/ne): ")).strip().lower()
            if confirm == 'ano':
                emergency_close_specific_symbols()
            else:
                print("Akce zrušena")
        elif choice == '4':
            print("👋 Ukončuji...")
            break
        else:
            print(error("❌ Neplatná volba"))

if __name__ == "__main__":
    main()
