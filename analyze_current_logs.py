#!/usr/bin/env python3
"""
🔍 ANALÝZA AKTUÁLNÍCH LOGŮ
Kontrola implementace sumarizace obchodů a všech nových funkcí
"""

import os
import re
from datetime import datetime
from colors import print_banner, success, error, highlight, warning

def read_latest_logs():
    """Čtení nejnovějších logů"""
    print_banner("🔍 ANALÝZA AKTUÁLNÍCH LOGŮ")
    
    log_files = [
        'logs/trading_bot.log',
        'trading_bot.log',
        'bot.log'
    ]
    
    latest_logs = []
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"📄 Nalezen log soubor: {highlight(log_file)}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # Posledních 200 řádků pro detailní analýzu
                recent_lines = lines[-200:] if len(lines) > 200 else lines
                latest_logs.extend(recent_lines)
                
                print(f"   📊 Načteno {len(recent_lines)} posledních řádků")
                
                # Informace o souboru
                file_size = os.path.getsize(log_file)
                print(f"   📏 Velikost souboru: {file_size} bytů")
                
            except Exception as e:
                print(f"   ❌ Chyba čtení {log_file}: {e}")
    
    if not latest_logs:
        print(f"⚠️ {warning('Žádné log soubory nenalezeny')}")
        return []
    
    print(f"\n📊 Celkem načteno {len(latest_logs)} řádků logů")
    return latest_logs

def analyze_new_features(logs):
    """Analýza nových funkcí"""
    print(f"\n🆕 {highlight('ANALÝZA NOVÝCH FUNKCÍ')}:")
    print("=" * 70)
    
    features_to_check = {
        'startup_analysis': {
            'patterns': [
                r'STARTUP ANALÝZA',
                r'PROVÁDÍM STARTUP ANALÝZU',
                r'STARTUP ANALÝZA TRHU'
            ],
            'description': 'Startup analýza trhu'
        },
        'trade_summary': {
            'patterns': [
                r'📋.*Provedeno.*obchodů:',
                r'nákup -\d+',
                r'prodej [+-]\d+',
                r'Celkem P&L:'
            ],
            'description': 'Sumarizace obchodů s P&L'
        },
        'debug_pnl': {
            'patterns': [
                r'🔍 DEBUG P&L:',
                r'Entry.*Exit.*Qty.*P&L'
            ],
            'description': 'DEBUG P&L výpisy'
        },
        'profit_taking': {
            'patterns': [
                r'💰 PROFIT TAKING',
                r'RSI.*≥ 55.*VÝBĚR ZISKU'
            ],
            'description': 'Profit taking při RSI ≥ 55'
        },
        'stop_loss': {
            'patterns': [
                r'🛑 STOP LOSS',
                r'RSI.*≤ 25.*OCHRANA'
            ],
            'description': 'Stop loss při RSI ≤ 25'
        },
        'emergency_sell': {
            'patterns': [
                r'🚨 EMERGENCY SELL',
                r'DOWNTREND.*MACD.*BEARISH'
            ],
            'description': 'Emergency sell v downtrend'
        },
        'silent_output': {
            'patterns': [
                r'💤 Žádná aktivita',
                r'Tichý monitoring',
                r'\033\[93m.*\033\[0m'  # Žlutý text
            ],
            'description': 'Tichý výstup s žlutou barvou'
        },
        'rsi_30_60': {
            'patterns': [
                r'RSI.*30.*60',
                r'RSI: 30/60',
                r'OVERSOLD.*30',
                r'OVERBOUGHT.*60'
            ],
            'description': 'RSI 30/60 parametry'
        }
    }
    
    results = {}
    
    for feature_name, feature_info in features_to_check.items():
        found_examples = []
        
        for log_line in logs:
            for pattern in feature_info['patterns']:
                if re.search(pattern, log_line, re.IGNORECASE):
                    found_examples.append(log_line.strip())
                    break
        
        results[feature_name] = {
            'count': len(found_examples),
            'examples': found_examples[:3],  # Max 3 příklady
            'description': feature_info['description']
        }
    
    # Výpis výsledků
    working_features = 0
    total_features = len(features_to_check)
    
    for feature_name, data in results.items():
        status_icon = "✅" if data['count'] > 0 else "❌"
        status_color = success if data['count'] > 0 else error
        
        if data['count'] > 0:
            working_features += 1
        
        print(f"\n{status_icon} {status_color(data['description'])}: {data['count']} případů")
        
        for example in data['examples']:
            # Zkrácení dlouhých řádků
            short_example = example[:100] + "..." if len(example) > 100 else example
            print(f"   📝 {short_example}")
    
    # Celkové hodnocení
    success_rate = (working_features / total_features) * 100
    
    print(f"\n📊 {highlight('CELKOVÉ HODNOCENÍ')}:")
    print(f"   🎯 Fungující funkce: {working_features}/{total_features} ({success_rate:.0f}%)")
    
    if success_rate >= 80:
        print(f"   ✅ {success('VÝBORNĚ! Většina funkcí funguje')}")
    elif success_rate >= 60:
        print(f"   ⚠️ {warning('DOBŘE, ale některé funkce chybí')}")
    else:
        print(f"   ❌ {error('PROBLÉM! Mnoho funkcí nefunguje')}")
    
    return results

def analyze_trading_activity(logs):
    """Analýza obchodní aktivity"""
    print(f"\n📈 {highlight('ANALÝZA OBCHODNÍ AKTIVITY')}:")
    print("=" * 70)
    
    # Vzory pro obchodní aktivitu
    trading_patterns = {
        'buy_signals': r'Action: BUY|📈.*BUY',
        'sell_signals': r'Action: SELL|❌.*SELL',
        'hold_signals': r'Action: HOLD|⏸️.*HOLD',
        'opened_positions': r'OTEVŘENO|🚀.*OTEVŘENO',
        'closed_positions': r'UZAVŘENO|💰.*UZAVŘENO',
        'balance_updates': r'Balance:.*USDT',
        'rsi_values': r'RSI: (\d+\.\d+)',
        'new_summary': r'📋.*Provedeno.*obchodů:'
    }
    
    activity_stats = {}
    rsi_values = []
    
    for pattern_name, pattern in trading_patterns.items():
        matches = []
        for log_line in logs:
            match = re.search(pattern, log_line)
            if match:
                matches.append(log_line.strip())
                
                # Speciální zpracování RSI hodnot
                if pattern_name == 'rsi_values' and match.groups():
                    try:
                        rsi_val = float(match.group(1))
                        if 0 <= rsi_val <= 100:
                            rsi_values.append(rsi_val)
                    except:
                        pass
        
        activity_stats[pattern_name] = matches
    
    # Výpis statistik
    for pattern_name, matches in activity_stats.items():
        count = len(matches)
        pattern_display = pattern_name.replace('_', ' ').title()
        
        if count > 0:
            if 'buy' in pattern_name or 'opened' in pattern_name:
                print(f"🚀 {success(pattern_display)}: {count}")
            elif 'sell' in pattern_name or 'closed' in pattern_name:
                print(f"❌ {error(pattern_display)}: {count}")
            else:
                print(f"📊 {highlight(pattern_display)}: {count}")
            
            # Ukázka posledních případů
            for match in matches[-2:]:
                short_match = match[:80] + "..." if len(match) > 80 else match
                print(f"   📝 {short_match}")
    
    # RSI analýza
    if rsi_values:
        print(f"\n📊 {highlight('RSI ANALÝZA')}:")
        min_rsi = min(rsi_values)
        max_rsi = max(rsi_values)
        avg_rsi = sum(rsi_values) / len(rsi_values)
        
        print(f"   📈 Počet RSI hodnot: {len(rsi_values)}")
        print(f"   📊 Min RSI: {min_rsi:.2f}")
        print(f"   📊 Max RSI: {max_rsi:.2f}")
        print(f"   📊 Průměr RSI: {avg_rsi:.2f}")
        
        # Kategorizace podle nových parametrů
        oversold_30 = len([r for r in rsi_values if r <= 30])
        overbought_60 = len([r for r in rsi_values if r >= 60])
        profit_zone_55 = len([r for r in rsi_values if r >= 55])
        stop_zone_25 = len([r for r in rsi_values if r <= 25])
        
        print(f"\n📊 Distribuce podle nových parametrů:")
        print(f"   🚀 Oversold (≤30): {oversold_30} ({oversold_30/len(rsi_values)*100:.1f}%)")
        print(f"   💰 Profit zone (≥55): {profit_zone_55} ({profit_zone_55/len(rsi_values)*100:.1f}%)")
        print(f"   ❌ Overbought (≥60): {overbought_60} ({overbought_60/len(rsi_values)*100:.1f}%)")
        print(f"   🛑 Stop zone (≤25): {stop_zone_25} ({stop_zone_25/len(rsi_values)*100:.1f}%)")
    
    return activity_stats

def check_recent_changes(logs):
    """Kontrola nedávných změn"""
    print(f"\n🕐 {highlight('KONTROLA NEDÁVNÝCH ZMĚN')}:")
    print("=" * 70)
    
    # Hledání časových razítek
    recent_entries = []
    
    for log_line in logs:
        # Hledání timestamp
        timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', log_line)
        if timestamp_match:
            timestamp_str = timestamp_match.group(1)
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                recent_entries.append({
                    'timestamp': timestamp,
                    'line': log_line.strip()
                })
            except:
                pass
    
    if recent_entries:
        # Seřazení podle času
        recent_entries.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Posledních 10 záznamů
        print(f"📅 Posledních 10 záznamů:")
        for entry in recent_entries[:10]:
            timestamp_str = entry['timestamp'].strftime('%H:%M:%S')
            short_line = entry['line'][:100] + "..." if len(entry['line']) > 100 else entry['line']
            print(f"   {timestamp_str}: {short_line}")
        
        # Časový rozsah
        if len(recent_entries) > 1:
            oldest = recent_entries[-1]['timestamp']
            newest = recent_entries[0]['timestamp']
            duration = newest - oldest
            
            print(f"\n⏰ Časový rozsah logů:")
            print(f"   📅 Od: {oldest.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   📅 Do: {newest.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   ⏱️ Doba: {duration}")
    else:
        print(f"⚠️ {warning('Žádné časové razítka nenalezena')}")

def provide_recommendations(analysis_results):
    """Doporučení na základě analýzy"""
    print(f"\n💡 {highlight('DOPORUČENÍ NA ZÁKLADĚ ANALÝZY')}:")
    print("=" * 70)
    
    recommendations = []
    
    # Kontrola nových funkcí
    if analysis_results.get('trade_summary', {}).get('count', 0) == 0:
        recommendations.append("❌ Sumarizace obchodů nefunguje - zkontrolujte implementaci")
    else:
        recommendations.append("✅ Sumarizace obchodů funguje správně")
    
    if analysis_results.get('startup_analysis', {}).get('count', 0) == 0:
        recommendations.append("❌ Startup analýza se nespustila - možná bot neběžel")
    else:
        recommendations.append("✅ Startup analýza funguje")
    
    if analysis_results.get('profit_taking', {}).get('count', 0) == 0:
        recommendations.append("⚠️ Žádný profit taking - možná RSI nedosáhl 55+")
    
    if analysis_results.get('stop_loss', {}).get('count', 0) == 0:
        recommendations.append("⚠️ Žádný stop loss - možná RSI neklesl pod 25")
    
    if analysis_results.get('debug_pnl', {}).get('count', 0) == 0:
        recommendations.append("❌ DEBUG P&L výpisy chybí - zkontrolujte prodejní logiku")
    
    # Obecná doporučení
    recommendations.extend([
        "🔍 Sledujte logy v reálném čase během běhu bota",
        "📊 Ověřte že nové RSI 30/60 parametry jsou aktivní",
        "💰 Čekejte na RSI signály pro testování nových funkcí",
        "🎯 Zkontrolujte že všechny nové funkce se projevují"
    ])
    
    for rec in recommendations:
        print(f"   {rec}")

def main():
    """Hlavní funkce"""
    print("🔍 ANALÝZA AKTUÁLNÍCH LOGŮ - KONTROLA IMPLEMENTACE")
    print("=" * 70)
    
    # Čtení logů
    logs = read_latest_logs()
    
    if not logs:
        print(f"\n❌ {error('Žádné logy k analýze')}")
        print(f"💡 Možné příčiny:")
        print(f"   • Bot neběžel od implementace nových funkcí")
        print(f"   • Log soubory jsou v jiném umístění")
        print(f"   • Bot není aktuálně spuštěný")
        return
    
    # Analýzy
    new_features_results = analyze_new_features(logs)
    trading_activity = analyze_trading_activity(logs)
    check_recent_changes(logs)
    provide_recommendations(new_features_results)
    
    # Celkový souhrn
    print(f"\n🎯 {highlight('CELKOVÝ SOUHRN')}:")
    print("=" * 70)
    
    working_features = sum(1 for result in new_features_results.values() if result['count'] > 0)
    total_features = len(new_features_results)
    success_rate = (working_features / total_features) * 100
    
    print(f"📊 Funkčnost nových funkcí: {working_features}/{total_features} ({success_rate:.0f}%)")
    
    if success_rate >= 80:
        print(f"🎉 {success('VÝBORNĚ! Implementace je úspěšná')}")
        print(f"   ✅ Většina nových funkcí funguje správně")
        print(f"   🚀 Bot je připraven na plný provoz")
    elif success_rate >= 60:
        print(f"⚠️ {warning('DOBŘE, ale je prostor pro zlepšení')}")
        print(f"   📊 Některé funkce možná potřebují doladění")
        print(f"   🔍 Sledujte další běhy bota")
    else:
        print(f"❌ {error('PROBLÉM! Implementace potřebuje opravu')}")
        print(f"   🔧 Zkontrolujte kód nových funkcí")
        print(f"   🚀 Možná je potřeba restart bota")

if __name__ == "__main__":
    main()
