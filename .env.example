# Trading Bot Configuration
# Zkopírujte tento soubor jako .env a vyplňte své hodnoty

# =============================================================================
# API KONFIGURACE
# =============================================================================
# Burza: binance nebo bybit
EXCHANGE=binance

# API klíče (získejte z burzy)
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here

# Demo účet (True pro testnet, False pro live trading)
SANDBOX=True

# =============================================================================
# TRADING PARAMETRY
# =============================================================================
# Symboly pro obchodování (oddělené čárkami)
SYMBOLS=BTC/USDT,ETH/USDT,ADA/USDT

# Timeframe pro analýzu (1m, 5m, 15m, 1h, 4h, 1d)
TIMEFRAME=1h

# =============================================================================
# RSI STRATEGIE
# =============================================================================
# Období pro výpočet RSI
RSI_PERIOD=14

# RSI úrovně
RSI_OVERSOLD=30
RSI_OVERBOUGHT=70

# =============================================================================
# RISK MANAGEMENT
# =============================================================================
# Maximální velikost pozice (% z kapitálu)
MAX_POSITION_SIZE=0.2

# Stop Loss (%)
STOP_LOSS_PERCENT=3.0

# Take Profit (%)
TAKE_PROFIT_PERCENT=5.0

# Leverage pro futures (začněte s 1x)
LEVERAGE=1

# =============================================================================
# BACKTESTING
# =============================================================================
# Období pro backtesting
BACKTEST_START=2023-01-01
BACKTEST_END=2024-01-01

# Počáteční kapitál pro backtesting (USDT)
INITIAL_BALANCE=10000

# =============================================================================
# SYSTÉMOVÉ NASTAVENÍ
# =============================================================================
# Úroveň logování (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Soubor pro logy
LOG_FILE=trading_bot.log

# Interval kontroly (sekundy)
CHECK_INTERVAL=60
