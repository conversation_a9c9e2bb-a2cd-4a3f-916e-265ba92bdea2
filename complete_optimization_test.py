#!/usr/bin/env python3
"""
KOMPLETNÍ TEST OPTIMALIZACE KÓDU
Ověření všech oprav a optimalizací
"""

import os
import sys
import importlib
import traceback

def test_imports():
    """Test všech importů"""
    print("🔍 TESTOVÁNÍ IMPORTŮ")
    print("=" * 50)
    
    modules_to_test = [
        'ccxt',
        'pandas', 
        'ta',
        'numpy',
        'python-dotenv',
        'config',
        'logger',
        'colors',
        'strategy',
        'risk_management',
        'bot',
        'intelligent_strategy_selector'
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            if module in ['ccxt', 'pandas', 'ta', 'numpy']:
                # Externí moduly
                importlib.import_module(module)
                print(f"✅ {module}")
            elif module == 'python-dotenv':
                # Speciální případ
                import dotenv
                print(f"✅ python-dotenv")
            else:
                # Lokální moduly
                importlib.import_module(module)
                print(f"✅ {module}")
                
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
        except Exception as e:
            print(f"⚠️ {module}: {e}")
    
    return len(failed_imports) == 0

def test_bot_initialization():
    """Test inicializace bota"""
    print(f"\n🤖 TESTOVÁNÍ INICIALIZACE BOTA")
    print("=" * 50)
    
    try:
        from bot import TradingBot
        
        # Test vytvoření instance
        bot = TradingBot()
        print("✅ TradingBot instance vytvořena")
        
        # Test atributů
        required_attributes = [
            'config', 'exchange', 'strategy', 'risk_manager',
            'strategy_selector', 'current_strategy_name', 
            'strategy_params', 'strategy_update_counter',
            'running', 'last_prices'
        ]
        
        for attr in required_attributes:
            if hasattr(bot, attr):
                print(f"✅ Atribut: {attr}")
            else:
                print(f"❌ Chybí atribut: {attr}")
                return False
        
        # Test metod
        required_methods = [
            'initialize_exchange', 'fetch_ohlcv', 'get_balance',
            'update_strategy_if_needed', 'place_market_order',
            'process_symbol', 'run_cycle', 'start', 'stop'
        ]
        
        for method in required_methods:
            if hasattr(bot, method) and callable(getattr(bot, method)):
                print(f"✅ Metoda: {method}")
            else:
                print(f"❌ Chybí metoda: {method}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba inicializace bota: {e}")
        traceback.print_exc()
        return False

def test_strategy_selector():
    """Test inteligentního výběru strategie"""
    print(f"\n🧠 TESTOVÁNÍ STRATEGY SELECTOR")
    print("=" * 50)
    
    try:
        from intelligent_strategy_selector import IntelligentStrategySelector
        
        # Test vytvoření instance
        selector = IntelligentStrategySelector()
        print("✅ IntelligentStrategySelector instance vytvořena")
        
        # Test strategií
        expected_strategies = [
            'TREND_FOLLOWING', 'RANGE_TRADING', 'SWING_TRADING',
            'SCALPING', 'DAY_TRADING', 'PRICE_ACTION'
        ]
        
        for strategy in expected_strategies:
            if strategy in selector.strategies:
                print(f"✅ Strategie: {strategy}")
            else:
                print(f"❌ Chybí strategie: {strategy}")
                return False
        
        # Test metod
        required_methods = [
            'analyze_market_conditions', 'select_optimal_strategy',
            'get_strategy_parameters'
        ]
        
        for method in required_methods:
            if hasattr(selector, method) and callable(getattr(selector, method)):
                print(f"✅ Metoda: {method}")
            else:
                print(f"❌ Chybí metoda: {method}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba strategy selector: {e}")
        traceback.print_exc()
        return False

def test_risk_management():
    """Test risk managementu"""
    print(f"\n🛡️ TESTOVÁNÍ RISK MANAGEMENT")
    print("=" * 50)
    
    try:
        from risk_management import RiskManager
        
        # Test vytvoření instance
        rm = RiskManager()
        print("✅ RiskManager instance vytvořena")
        
        # Test inteligentního sizing
        test_cases = [
            {
                'symbol': 'BTC/USDT',
                'side': 'buy',
                'quantity': 0.01,
                'price': 100000.0,
                'balance': 15000.0
            },
            {
                'symbol': 'ADA/USDT', 
                'side': 'sell',
                'quantity': 5000.0,
                'price': 0.62,
                'balance': 15000.0
            }
        ]
        
        for case in test_cases:
            result = rm.validate_trade(
                case['symbol'], case['side'], case['quantity'],
                case['price'], case['balance']
            )
            
            if result and 'valid' in result:
                print(f"✅ Validace {case['side']} {case['symbol']}: {result['valid']}")
                if result.get('adjusted', False):
                    print(f"   🔄 Upraveno: {result['adjusted_quantity']:.6f}")
            else:
                print(f"❌ Chyba validace {case['side']} {case['symbol']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba risk management: {e}")
        traceback.print_exc()
        return False

def test_colors():
    """Test barevného výstupu"""
    print(f"\n🎨 TESTOVÁNÍ BAREVNÉHO VÝSTUPU")
    print("=" * 50)
    
    try:
        from colors import (
            success, error, warning, highlight,
            buy_signal, sell_signal, hold_signal,
            format_rsi, print_banner
        )
        
        # Test základních funkcí
        print(f"✅ {success('SUCCESS test')}")
        print(f"✅ {error('ERROR test')}")
        print(f"✅ {warning('WARNING test')}")
        print(f"✅ {highlight('HIGHLIGHT test')}")
        
        # Test signálů
        print(f"✅ {buy_signal(' BUY ')} signál")
        print(f"✅ {sell_signal(' SELL ')} signál")
        print(f"✅ {hold_signal(' HOLD ')} signál - OPRAVENO (černý text)")
        
        # Test RSI formátování
        rsi_values = [35, 50, 65]
        for rsi in rsi_values:
            formatted = format_rsi(rsi, oversold=45, overbought=55)
            print(f"✅ RSI {rsi}: {formatted}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba colors: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test konfigurace"""
    print(f"\n⚙️ TESTOVÁNÍ KONFIGURACE")
    print("=" * 50)
    
    try:
        from config import Config
        
        # Test agresivních parametrů
        print(f"✅ RSI_OVERSOLD: {Config.RSI_OVERSOLD} (očekáváno: 45)")
        print(f"✅ RSI_OVERBOUGHT: {Config.RSI_OVERBOUGHT} (očekáváno: 55)")
        print(f"✅ TIMEFRAME: {Config.TIMEFRAME} (očekáváno: 3m)")
        print(f"✅ CHECK_INTERVAL: {Config.CHECK_INTERVAL} (očekáváno: 20)")
        print(f"✅ MAX_POSITION_SIZE: {Config.MAX_POSITION_SIZE} (očekáváno: 0.25)")
        
        # Ověření agresivních hodnot
        if (Config.RSI_OVERSOLD == 45 and Config.RSI_OVERBOUGHT == 55 and
            Config.TIMEFRAME == '3m' and Config.CHECK_INTERVAL == 20):
            print("✅ Agresivní parametry správně nastavené")
            return True
        else:
            print("⚠️ Některé parametry nejsou optimální")
            return False
        
    except Exception as e:
        print(f"❌ Chyba konfigurace: {e}")
        traceback.print_exc()
        return False

def test_file_structure():
    """Test struktury souborů"""
    print(f"\n📁 TESTOVÁNÍ STRUKTURY SOUBORŮ")
    print("=" * 50)
    
    required_files = [
        'main.py', 'bot.py', 'strategy.py', 'config.py',
        'risk_management.py', 'logger.py', 'colors.py',
        'intelligent_strategy_selector.py', 'optimized_bot.py',
        '.env', 'requirements.txt'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - CHYBÍ")
            missing_files.append(file)
    
    return len(missing_files) == 0

def show_optimization_summary():
    """Souhrn optimalizací"""
    print(f"\n📊 SOUHRN OPTIMALIZACÍ")
    print("=" * 50)
    
    optimizations = [
        "✅ Opravené importy s error handling",
        "✅ Přidané type hints pro lepší code quality",
        "✅ Inteligentní výběr strategie podle trendu",
        "✅ Agresivní RSI parametry (45/55)",
        "✅ Rychlejší timeframe (3m) a interval (20s)",
        "✅ Inteligentní position sizing ('aspoň kolik můžu')",
        "✅ Opravený barevný výstup (černý text na žlutém)",
        "✅ Robustní error handling",
        "✅ Optimalizované výpočty indikátorů",
        "✅ Automatické přepínání strategií",
        "✅ Vylepšený risk management",
        "✅ Discord notifikace o změnách strategií"
    ]
    
    for opt in optimizations:
        print(f"   {opt}")

def main():
    """Hlavní funkce"""
    print("🔧 KOMPLETNÍ TEST OPTIMALIZACE KÓDU")
    print("=" * 70)
    
    tests = [
        ("Importy", test_imports),
        ("Bot inicializace", test_bot_initialization),
        ("Strategy Selector", test_strategy_selector),
        ("Risk Management", test_risk_management),
        ("Barevný výstup", test_colors),
        ("Konfigurace", test_configuration),
        ("Struktura souborů", test_file_structure)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"\n✅ {test_name}: PROŠEL")
            else:
                print(f"\n❌ {test_name}: SELHAL")
        except Exception as e:
            print(f"\n💥 {test_name}: CHYBA - {e}")
    
    print(f"\n🎯 VÝSLEDKY TESTŮ")
    print("=" * 50)
    print(f"Prošlo: {passed_tests}/{total_tests} testů")
    print(f"Úspěšnost: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print(f"\n🎉 VŠECHNY TESTY PROŠLY!")
        print(f"✅ Kód je kompletně optimalizovaný")
        print(f"✅ Všechny chyby opravené")
        print(f"✅ Bot připraven k spuštění")
        
        show_optimization_summary()
        
        print(f"\n🚀 SPUŠTĚNÍ OPTIMALIZOVANÉHO BOTA:")
        print(f"python main.py --mode live")
        
    else:
        print(f"\n⚠️ NĚKTERÉ TESTY SELHALY")
        print(f"Zkontrolujte chyby výše a opravte je")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
