import os
from dotenv import load_dotenv

# Načtení environment variables
load_dotenv()

class Config:
    """Konfigurace trading bota"""
    
    # API konfigurace
    EXCHANGE = os.getenv('EXCHANGE', 'binance')  # binance nebo bybit
    API_KEY = os.getenv('API_KEY', '')
    API_SECRET = os.getenv('API_SECRET', '')
    SANDBOX = os.getenv('SANDBOX', 'True').lower() == 'true'  # Demo účet
    
    # Trading parametry
    SYMBOLS = os.getenv('SYMBOLS', 'BTC/USDT,ETH/USDT,ADA/USDT').split(',')
    TIMEFRAME = os.getenv('TIMEFRAME', '1h')  # 1m, 5m, 15m, 1h, 4h, 1d
    
    # RSI strategie
    RSI_PERIOD = int(os.getenv('RSI_PERIOD', '14'))
    RSI_OVERSOLD = int(os.getenv('RSI_OVERSOLD', '35'))  # <PERSON><PERSON><PERSON> konzervativní
    RSI_OVERBOUGHT = int(os.getenv('RSI_OVERBOUGHT', '65'))  # Více signálů
    
    # Risk management
    MAX_POSITION_SIZE = float(os.getenv('MAX_POSITION_SIZE', '0.2'))  # 20% kapitálu
    STOP_LOSS_PERCENT = float(os.getenv('STOP_LOSS_PERCENT', '3.0'))  # 3%
    TAKE_PROFIT_PERCENT = float(os.getenv('TAKE_PROFIT_PERCENT', '5.0'))  # 5%
    
    # Leverage (pro futures)
    LEVERAGE = int(os.getenv('LEVERAGE', '1'))  # Začneme s 1x
    
    # Backtesting
    BACKTEST_START = os.getenv('BACKTEST_START', '2023-01-01')
    BACKTEST_END = os.getenv('BACKTEST_END', '2024-01-01')
    INITIAL_BALANCE = float(os.getenv('INITIAL_BALANCE', '10000'))  # USDT
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'trading_bot.log')
    
    # Interval kontroly (sekundy)
    CHECK_INTERVAL = int(os.getenv('CHECK_INTERVAL', '60'))
    
    @classmethod
    def validate(cls):
        """Validace konfigurace"""
        if not cls.API_KEY or not cls.API_SECRET:
            raise ValueError("API_KEY a API_SECRET musí být nastaveny")
        
        if cls.EXCHANGE not in ['binance', 'bybit']:
            raise ValueError("EXCHANGE musí být 'binance' nebo 'bybit'")
        
        if cls.MAX_POSITION_SIZE <= 0 or cls.MAX_POSITION_SIZE > 1:
            raise ValueError("MAX_POSITION_SIZE musí být mezi 0 a 1")
        
        return True

# Binance testnet konfigurace
BINANCE_TESTNET = {
    'apiKey': Config.API_KEY,
    'secret': Config.API_SECRET,
    'sandbox': True,
    'options': {
        'defaultType': 'future',
    }
}

# Bybit testnet konfigurace
BYBIT_TESTNET = {
    'apiKey': Config.API_KEY,
    'secret': Config.API_SECRET,
    'sandbox': True,
    'testnet': True,
    'hostname': 'api-testnet.bybit.com',
    'urls': {
        'api': {
            'public': 'https://api-testnet.bybit.com',
            'private': 'https://api-testnet.bybit.com',
        }
    }
}
