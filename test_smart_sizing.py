#!/usr/bin/env python3
"""
Test inteligentn<PERSON>ho sizing - "aspoň kolik můžu"
"""

def test_smart_sizing():
    """Test vaší logiky"""
    print("🧠 TEST INTELIGENTNÍHO SIZING")
    print("=" * 50)
    
    try:
        from risk_management import RiskManager
        
        # Vytvoření risk managera
        rm = RiskManager(max_position_size=0.25)  # 25% limit
        
        # Test scénář
        balance = 15000.0  # 15k USDT
        max_limit = balance * 0.25  # 3750 USDT limit
        
        print(f"📊 Balance: {balance:,.2f} USDT")
        print(f"📊 Max limit (25%): {max_limit:,.2f} USDT")
        
        # Test případy
        test_cases = [
            {
                'name': 'ADA - Překračuje limit',
                'symbol': 'ADA/USDT',
                'price': 0.62,
                'quantity': 4770.0,  # = 2957.20 USDT (pod limitem)
                'side': 'sell'
            },
            {
                'name': 'BTC - Velký nákup',
                'symbol': 'BTC/USDT', 
                'price': 107000.0,
                'quantity': 0.05,  # = 5350 USDT (nad limitem)
                'side': 'buy'
            },
            {
                'name': 'ETH - Normální obchod',
                'symbol': 'ETH/USDT',
                'price': 3200.0,
                'quantity': 1.0,  # = 3200 USDT (pod limitem)
                'side': 'buy'
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n{i}️⃣ TEST: {case['name']}")
            print(f"   📊 {case['symbol']}: {case['quantity']:.6f} @ ${case['price']:,.2f}")
            
            trade_value = case['quantity'] * case['price']
            print(f"   💰 Hodnota obchodu: {trade_value:,.2f} USDT")
            
            # Simulace pozice pro SELL
            if case['side'] == 'sell':
                rm.open_positions[case['symbol']] = {
                    'side': 'long',
                    'quantity': case['quantity'],
                    'entry_price': case['price']
                }
            
            # Test validace
            result = rm.validate_trade(
                case['symbol'], 
                case['side'], 
                case['quantity'], 
                case['price'], 
                balance
            )
            
            print(f"   ✅ Validace: {result['valid']}")
            print(f"   📝 Důvod: {result['reason']}")
            
            if result.get('adjusted', False):
                original = result['original_quantity']
                adjusted = result['adjusted_quantity']
                adjusted_value = adjusted * case['price']
                
                print(f"   🔄 UPRAVENO:")
                print(f"      📊 Původní: {original:.6f} = {trade_value:,.2f} USDT")
                print(f"      📊 Upraveno: {adjusted:.6f} = {adjusted_value:,.2f} USDT")
                print(f"      📈 Rozdíl: {(original - adjusted):.6f}")
                
                # Vaše logika test
                if trade_value > max_limit:
                    expected_quantity = max_limit / case['price']
                    actual_quantity = adjusted
                    
                    print(f"   🧠 VAŠE LOGIKA:")
                    print(f"      📊 Požadováno: {trade_value:,.2f} USDT")
                    print(f"      📊 Max limit: {max_limit:,.2f} USDT")
                    print(f"      📊 Výsledek: {adjusted_value:,.2f} USDT")
                    
                    if abs(actual_quantity - expected_quantity) < 0.000001:
                        print(f"      ✅ LOGIKA FUNGUJE!")
                    else:
                        print(f"      ❌ Chyba v logice")
                else:
                    print(f"   ✅ Pod limitem - žádná úprava potřebná")
            else:
                print(f"   ✅ Obchod prošel bez úprav")
        
        print(f"\n🎉 TEST DOKONČEN!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba testu: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_logic_explanation():
    """Vysvětlení logiky"""
    print(f"\n🧠 VAŠE LOGIKA IMPLEMENTOVÁNA:")
    print(f"=" * 50)
    
    print(f"```python")
    print(f"if požadovaná_prodejní_pozice > max_limit:")
    print(f"    prodejní_objem = max_limit")
    print(f"else:")
    print(f"    prodejní_objem = požadovaná_prodejní_pozice")
    print(f"```")
    
    print(f"\n📊 PRAKTICKÝ PŘÍKLAD:")
    print(f"   • Balance: 15,000 USDT")
    print(f"   • Max limit (25%): 3,750 USDT")
    print(f"   • ADA cena: $0.62")
    print(f"   • Požadováno: 4,770 ADA = 2,957 USDT")
    
    print(f"\n🔄 VÝPOČET:")
    print(f"   • 2,957 USDT < 3,750 USDT ✅")
    print(f"   • Prodejní objem = 4,770 ADA (celá pozice)")
    
    print(f"\n📈 PŘÍKLAD NAD LIMITEM:")
    print(f"   • BTC cena: $107,000")
    print(f"   • Požadováno: 0.05 BTC = 5,350 USDT")
    print(f"   • 5,350 USDT > 3,750 USDT ❌")
    print(f"   • Max množství: 3,750 / 107,000 = 0.035 BTC")
    print(f"   • Prodejní objem = 0.035 BTC (max co můžu)")
    
    print(f"\n✅ VÝHODY:")
    print(f"   • Žádný obchod není úplně zamítnut")
    print(f"   • Maximální využití dostupného limitu")
    print(f"   • Jednoduchá a intuitivní logika")
    print(f"   • 'Aspoň kolik můžu' přístup")

def main():
    """Hlavní funkce"""
    if test_smart_sizing():
        show_logic_explanation()
        
        print(f"\n🚀 INTELIGENTNÍ SIZING AKTIVNÍ!")
        print(f"Bot nyní použije 'aspoň kolik můžu' logiku")
        print(f"Žádný obchod nebude úplně zamítnut")
    else:
        print(f"\n❌ TEST SELHAL")

if __name__ == "__main__":
    main()
