#!/usr/bin/env python3
"""
🎯 TEST NOVÝCH PARAMETRŮ RSI 30/60
Testování optimalizovaných parametrů pro bezpečnost + ziskovost
"""

from colors import print_banner, success, error, highlight, warning

def show_new_parameters():
    """Zobrazení nových parametrů"""
    print_banner("🎯 NOVÉ OPTIMALIZOVANÉ PARAMETRY")
    
    print(f"\n📊 {highlight('HLAVNÍ ZMĚNY')}:")
    print("=" * 60)
    
    changes = {
        "📈 RSI PARAMETRY": {
            "před": "RSI 25/75 (pří<PERSON>š konzervativní)",
            "po": "RSI 30/60 (optimální bezpečnost + ziskovost)",
            "důvod": "<PERSON><PERSON><PERSON> obcho<PERSON>, ale stále bezpečné"
        },
        "💰 POSITION SIZE": {
            "před": "25% → 10% (po ztrátách)",
            "po": "10% (z<PERSON>st<PERSON><PERSON><PERSON> konzervativní)",
            "důvod": "O<PERSON>rana před velkými ztrátami jako ADA"
        },
        "⏰ TIMEFRAME": {
            "před": "3m (p<PERSON><PERSON><PERSON><PERSON> r<PERSON>) → 15m",
            "po": "15m (zůstává stabilní)",
            "důvod": "Stabilnější signály, méně false positives"
        },
        "🔄 CHECK INTERVAL": {
            "před": "20s (příliš častý) → 60s",
            "po": "60s (zůstává pomalý)",
            "důvod": "Méně stress testování API"
        }
    }
    
    for category, info in changes.items():
        print(f"\n{category}:")
        print(f"   📊 Před: {info['před']}")
        print(f"   ✅ Po: {success(info['po'])}")
        print(f"   💡 Důvod: {info['důvod']}")

def show_new_trading_logic():
    """Nová obchodní logika"""
    print(f"\n🧠 {highlight('NOVÁ OBCHODNÍ LOGIKA')}:")
    print("=" * 60)
    
    logic_flow = [
        {
            'condition': 'RSI ≤ 30 + Uptrend + MACD Bullish',
            'action': '🚀 TRIPLE BUY',
            'confidence': '90-100%',
            'reason': 'Nejlepší nákupní příležitost'
        },
        {
            'condition': 'RSI ≤ 30 + Uptrend',
            'action': '✅ STRONG BUY',
            'confidence': '80-95%',
            'reason': 'Silný nákupní signál'
        },
        {
            'condition': 'RSI ≤ 20 (extrémní oversold)',
            'action': '🚀 EXTREME BUY',
            'confidence': '85-100%',
            'reason': 'Nákup i bez uptrend'
        },
        {
            'condition': 'RSI ≤ 30 + MACD Bullish',
            'action': '📈 MOMENTUM BUY',
            'confidence': '75-90%',
            'reason': 'Momentum potvrzuje nákup'
        },
        {
            'condition': 'RSI ≤ 30 (základní)',
            'action': '⚡ OVERSOLD BUY',
            'confidence': '60-80%',
            'reason': 'Základní oversold nákup'
        }
    ]
    
    print(f"\n🚀 {success('NÁKUPNÍ SIGNÁLY')}:")
    for logic in logic_flow:
        print(f"\n📊 {logic['condition']}:")
        print(f"   🎯 Akce: {logic['action']}")
        print(f"   📈 Confidence: {logic['confidence']}")
        print(f"   💡 Důvod: {logic['reason']}")
    
    sell_logic = [
        {
            'condition': 'RSI ≥ 55 (mírně overbought)',
            'action': '💰 PROFIT TAKING',
            'confidence': '70-90%',
            'reason': 'Výběr zisku při dobrém P&L'
        },
        {
            'condition': 'RSI ≤ 25 (velmi oversold)',
            'action': '🛑 STOP LOSS',
            'confidence': '80-100%',
            'reason': 'Ochrana před velkou ztrátou'
        },
        {
            'condition': 'Downtrend + MACD Bearish',
            'action': '🚨 EMERGENCY SELL',
            'confidence': '90%',
            'reason': 'Ochrana kapitálu'
        },
        {
            'condition': 'RSI ≥ 60 (overbought)',
            'action': '❌ SELL',
            'confidence': '80-95%',
            'reason': 'Standardní prodej'
        }
    ]
    
    print(f"\n❌ {error('PRODEJNÍ SIGNÁLY')}:")
    for logic in sell_logic:
        print(f"\n📊 {logic['condition']}:")
        print(f"   🎯 Akce: {logic['action']}")
        print(f"   📈 Confidence: {logic['confidence']}")
        print(f"   💡 Důvod: {logic['reason']}")

def show_risk_management():
    """Risk management"""
    print(f"\n🛡️ {highlight('VYLEPŠENÝ RISK MANAGEMENT')}:")
    print("=" * 60)
    
    risk_features = {
        "💰 POSITION SIZING": [
            "Maximálně 10% balance na pozici",
            "ADA pozice byla 53% - už se nestane!",
            "Automatické omezení velikosti",
            "Ochrana před velkými ztrátami"
        ],
        "🛑 STOP LOSS": [
            "RSI ≤ 25 = okamžitý stop loss",
            "Downtrend + MACD bearish = emergency sell",
            "Ochrana před ztrátami > 5%",
            "Rychlé uzavření problémových pozic"
        ],
        "💰 PROFIT TAKING": [
            "RSI ≥ 55 = výběr zisku",
            "Nečeká na RSI 60 = dřívější zisky",
            "Lepší realizace zisků",
            "Ochrana před reversal"
        ],
        "📊 MONITORING": [
            "Debug P&L výpočtu",
            "Sledování velikosti pozic",
            "Okamžité notifikace problémů",
            "Detailní analýza obchodů"
        ]
    }
    
    for category, features in risk_features.items():
        print(f"\n{category}:")
        for feature in features:
            print(f"   ✅ {feature}")

def show_expected_results():
    """Očekávané výsledky"""
    print(f"\n📈 {highlight('OČEKÁVANÉ VÝSLEDKY')}:")
    print("=" * 60)
    
    print(f"\n✅ {success('POZITIVNÍ ZMĚNY')}:")
    positives = [
        "Více obchodů než RSI 25/75",
        "Bezpečnější než RSI 45/55",
        "Dřívější výběr zisků (RSI 55)",
        "Rychlejší stop loss (RSI 25)",
        "Menší pozice (10% vs 25%)",
        "Stabilnější signály (15m timeframe)",
        "Lepší P&L management",
        "Ochrana před velkými ztrátami"
    ]
    
    for positive in positives:
        print(f"   🎯 {positive}")
    
    print(f"\n📊 {highlight('OČEKÁVANÁ AKTIVITA')}:")
    activity_levels = {
        "🚀 NÁKUPY": "RSI ≤ 30 (častější než 25, méně než 45)",
        "💰 PROFIT TAKING": "RSI ≥ 55 (dřívější než 60/75)",
        "🛑 STOP LOSS": "RSI ≤ 25 (rychlejší ochrana)",
        "📊 CELKOVÁ AKTIVITA": "Vyvážená - bezpečná ale aktivní"
    }
    
    for activity, description in activity_levels.items():
        print(f"   {activity}: {description}")

def show_comparison():
    """Porovnání parametrů"""
    print(f"\n📊 {highlight('POROVNÁNÍ PARAMETRŮ')}:")
    print("=" * 60)
    
    comparison = {
        "RSI OVERSOLD": {
            "Agresivní (45)": "❌ Příliš častý, riskantní",
            "Konzervativní (25)": "⚠️ Příliš vzácný, málo obchodů", 
            "Optimální (30)": "✅ Vyvážený, bezpečný + aktivní"
        },
        "RSI OVERBOUGHT": {
            "Agresivní (55)": "❌ Příliš brzký prodej",
            "Konzervativní (75)": "⚠️ Příliš pozdní prodej",
            "Optimální (60)": "✅ Dobrý timing pro zisky"
        },
        "POSITION SIZE": {
            "Agresivní (25%)": "❌ Velké ztráty (ADA -322 USDT)",
            "Konzervativní (10%)": "✅ Bezpečné, kontrolované riziko",
            "Optimální (10%)": "✅ Ideální pro recovery"
        }
    }
    
    for parameter, options in comparison.items():
        print(f"\n📈 {parameter}:")
        for option, assessment in options.items():
            print(f"   {option}: {assessment}")

def main():
    """Hlavní funkce"""
    print("🎯 TEST NOVÝCH PARAMETRŮ RSI 30/60")
    print("=" * 70)
    
    show_new_parameters()
    show_new_trading_logic()
    show_risk_management()
    show_expected_results()
    show_comparison()
    
    print(f"\n🎉 {success('NOVÉ PARAMETRY IMPLEMENTOVÁNY!')}")
    print(f"   📊 RSI 30/60 - optimální bezpečnost + ziskovost")
    print(f"   💰 Position size 10% - ochrana před velkými ztrátami")
    print(f"   🛡️ Vylepšený risk management")
    print(f"   📈 Lepší profit taking (RSI 55)")
    print(f"   🛑 Rychlejší stop loss (RSI 25)")
    
    print(f"\n🚀 {highlight('SPUŠTĚNÍ S NOVÝMI PARAMETRY')}:")
    print(f"python main.py --mode live")
    
    print(f"\n💡 {highlight('OČEKÁVANÉ CHOVÁNÍ')}:")
    print(f"   • Více obchodů než RSI 25/75")
    print(f"   • Bezpečnější než RSI 45/55")
    print(f"   • Dřívější výběr zisků")
    print(f"   • Rychlejší stop loss")
    print(f"   • Menší pozice = menší riziko")

if __name__ == "__main__":
    main()
