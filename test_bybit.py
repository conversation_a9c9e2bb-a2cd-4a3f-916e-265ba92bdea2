#!/usr/bin/env python3
"""
Test Bybit API připojení
"""

import ccxt
import sys
import os
from dotenv import load_dotenv

# Načtení environment variables
load_dotenv()

def test_bybit_connection():
    """Test různých konfigurací Bybit"""
    
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    print("🔍 Testování Bybit API připojení...")
    print(f"API Key: {api_key[:8]}...")
    print(f"API Secret: {api_secret[:8]}...")
    
    # Test 1: Testnet konfigurace
    print("\n1️⃣ Test Bybit Testnet...")
    try:
        exchange = ccxt.bybit({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': True,
            'testnet': True,
        })
        
        balance = exchange.fetch_balance()
        print("✅ Testnet připojení úspěšné!")
        print(f"Balance: {balance}")
        return True
        
    except Exception as e:
        print(f"❌ Testnet chyba: {e}")
    
    # Test 2: Live s demo módem
    print("\n2️⃣ Test Bybit Live (demo mód)...")
    try:
        exchange = ccxt.bybit({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': False,
            'testnet': False,
        })
        
        # Pouze čtení balance - žádné obchody
        balance = exchange.fetch_balance()
        print("✅ Live připojení úspěšné!")
        print(f"USDT Balance: {balance.get('USDT', {}).get('free', 0)}")
        
        # Test získání ticker
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"BTC/USDT cena: {ticker['last']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Live chyba: {e}")
    
    # Test 3: Základní připojení bez sandbox
    print("\n3️⃣ Test základní připojení...")
    try:
        exchange = ccxt.bybit({
            'apiKey': api_key,
            'secret': api_secret,
        })
        
        # Test veřejných dat (bez autentifikace)
        ticker = exchange.fetch_ticker('BTC/USDT')
        print("✅ Veřejná data OK!")
        print(f"BTC/USDT cena: {ticker['last']}")
        
        # Test autentifikace
        balance = exchange.fetch_balance()
        print("✅ Autentifikace OK!")
        print(f"Account type: {balance.get('info', {}).get('accountType', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Základní připojení chyba: {e}")
    
    return False

def test_symbols():
    """Test dostupných symbolů"""
    print("\n📊 Test symbolů...")
    
    try:
        exchange = ccxt.bybit()
        markets = exchange.load_markets()
        
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        for symbol in symbols:
            if symbol in markets:
                print(f"✅ {symbol} - dostupný")
                market = markets[symbol]
                print(f"   Type: {market.get('type', 'N/A')}")
                print(f"   Active: {market.get('active', 'N/A')}")
            else:
                print(f"❌ {symbol} - nedostupný")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při načítání symbolů: {e}")
        return False

def main():
    """Hlavní funkce"""
    print("🤖 BYBIT API TEST")
    print("=" * 40)
    
    # Test symbolů (bez autentifikace)
    test_symbols()
    
    # Test připojení s API klíči
    if test_bybit_connection():
        print("\n🎉 Alespoň jedno připojení funguje!")
        print("\n📋 Doporučení:")
        print("1. Pokud funguje live připojení, můžete pokračovat")
        print("2. Nastavte SANDBOX=False v .env pro live trading")
        print("3. POZOR: Live trading používá reálné peníze!")
    else:
        print("\n❌ Žádné připojení nefunguje")
        print("\n🔧 Možná řešení:")
        print("1. Zkontrolujte API klíče")
        print("2. Ověřte oprávnění API klíčů")
        print("3. Zkuste vytvořit nové API klíče")

if __name__ == "__main__":
    main()
