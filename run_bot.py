#!/usr/bin/env python3
"""
Jednoduchý spouštěcí skript pro Trading Bot
Poskytuje interaktivní menu pro snadné ovládání
"""

import os
import sys
import subprocess
from datetime import datetime

def print_banner():
    """Výpis banneru"""
    print("\n" + "="*60)
    print("🤖 TRADING BOT - RSI STRATEGIE")
    print("="*60)
    print("Automatický trading bot pro futures obchodování")
    print("Podporuje Binance a Bybit (demo i live účty)")
    print("="*60)

def check_env_file():
    """Kontrola existence .env souboru"""
    if not os.path.exists('.env'):
        print("\n❌ Soubor .env neexistuje!")
        print("📋 Zkopírujte .env.example jako .env a vyplňte své API klíče:")
        print("   cp .env.example .env")
        print("   # Poté upravte .env soubor")
        return False
    return True

def check_dependencies():
    """Kontrola nainstalovaných závislostí"""
    try:
        import ccxt
        import pandas
        import ta
        return True
    except ImportError as e:
        print(f"\n❌ Chybí závislosti: {e}")
        print("📦 Nainstalujte závislosti:")
        print("   pip install -r requirements.txt")
        return False

def run_command(command):
    """Spuštění příkazu"""
    try:
        print(f"\n🚀 Spouštím: {command}")
        print("-" * 40)
        result = subprocess.run([sys.executable] + command.split()[1:], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Chyba při spuštění: {e}")
        return False

def show_menu():
    """Zobrazení hlavního menu"""
    print("\n📋 HLAVNÍ MENU:")
    print("1. 🔍 Test připojení k burze")
    print("2. 📊 Spustit backtesting")
    print("3. 🎮 Demo režim (bez API klíčů)")
    print("4. 🤖 Spustit live trading (DEMO)")
    print("5. ⚠️  Spustit live trading (LIVE)")
    print("6. 📁 Zobrazit logy")
    print("7. ⚙️  Zobrazit konfiguraci")
    print("0. 🚪 Ukončit")
    print("-" * 40)

def show_logs():
    """Zobrazení logů"""
    log_file = "logs/trading_bot.log"
    if os.path.exists(log_file):
        print(f"\n📄 Posledních 20 řádků z {log_file}:")
        print("-" * 40)
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-20:]:
                    print(line.rstrip())
        except Exception as e:
            print(f"❌ Chyba při čtení logů: {e}")
    else:
        print(f"\n❌ Log soubor {log_file} neexistuje")

def show_config():
    """Zobrazení konfigurace"""
    if not os.path.exists('.env'):
        print("\n❌ Soubor .env neexistuje")
        return
    
    print("\n⚙️  AKTUÁLNÍ KONFIGURACE:")
    print("-" * 40)
    
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Skrytí API klíčů
                    if 'API_KEY' in line or 'API_SECRET' in line:
                        key, value = line.split('=', 1)
                        if value and value != 'your_api_key_here' and value != 'your_api_secret_here':
                            print(f"{key}=***HIDDEN***")
                        else:
                            print(line)
                    else:
                        print(line)
    except Exception as e:
        print(f"❌ Chyba při čtení konfigurace: {e}")

def confirm_live_trading():
    """Potvrzení live tradingu"""
    print("\n⚠️  POZOR: LIVE TRADING!")
    print("🔥 Budete obchodovat s reálnými penězi!")
    print("💰 Můžete ztratit své peníze!")
    print("\n📋 Před spuštěním se ujistěte, že:")
    print("   ✅ Máte nastaveno SANDBOX=False v .env")
    print("   ✅ Používáte live API klíče")
    print("   ✅ Otestovali jste strategii na demo účtu")
    print("   ✅ Rozumíte rizikům")
    
    response = input("\n❓ Opravdu chcete spustit live trading? (ano/ne): ")
    return response.lower() in ['ano', 'yes', 'y']

def main():
    """Hlavní funkce"""
    print_banner()
    
    # Kontroly
    if not check_env_file():
        return
    
    if not check_dependencies():
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("👉 Vyberte možnost (0-7): ").strip()
            
            if choice == '0':
                print("\n👋 Ukončuji program...")
                break
                
            elif choice == '1':
                print("\n🔍 Testování připojení...")
                run_command("python main.py --mode test")
                
            elif choice == '2':
                print("\n📊 Spouštím backtesting...")
                print("⏳ Může to trvat několik minut...")
                run_command("python main.py --mode backtest")
                
            elif choice == '3':
                print("\n🎮 Spouštím Demo režim...")
                print("💡 Simulace obchodování bez API klíčů")
                days = input("📅 Počet dní pro simulaci (výchozí 7): ").strip()
                if not days:
                    days = "7"
                run_command(f"python demo_mode.py {days}")

            elif choice == '4':
                print("\n🤖 Spouštím DEMO trading...")
                print("💡 Ujistěte se, že máte SANDBOX=True v .env")
                input("📋 Stiskněte Enter pro pokračování...")
                run_command("python main.py --mode live")

            elif choice == '5':
                if confirm_live_trading():
                    print("\n🔥 Spouštím LIVE trading...")
                    run_command("python main.py --mode live")
                else:
                    print("\n✅ Live trading zrušen")

            elif choice == '6':
                show_logs()

            elif choice == '7':
                show_config()
                
            else:
                print("\n❌ Neplatná volba!")
                
        except KeyboardInterrupt:
            print("\n\n👋 Program přerušen uživatelem")
            break
        except Exception as e:
            print(f"\n❌ Neočekávaná chyba: {e}")
        
        if choice != '0':
            input("\n📋 Stiskněte Enter pro návrat do menu...")

if __name__ == "__main__":
    main()
