#!/usr/bin/env python3
"""
🔍 KONTROLA POSLEDNÍCH LOGŮ
Analýza logů po implementaci RSI 30/60 a vylepšené logiky
"""

import os
import re
from datetime import datetime, timedelta
from colors import print_banner, success, error, highlight, warning

def read_recent_logs():
    """Čtení posledních logů"""
    print_banner("🔍 KONTROLA POSLEDNÍCH LOGŮ")
    
    log_files = [
        'trading_bot.log',
        'bot.log',
        'logs/trading_bot.log',
        'logs/bot.log'
    ]
    
    recent_logs = []
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"📄 Nalezen log soubor: {highlight(log_file)}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # Posledních 100 řádků
                recent_lines = lines[-100:] if len(lines) > 100 else lines
                recent_logs.extend(recent_lines)
                
                print(f"   📊 Načteno {len(recent_lines)} posledních řádk<PERSON>")
                
            except Exception as e:
                print(f"   ❌ Chyba čtení {log_file}: {e}")
    
    if not recent_logs:
        print(f"⚠️ {warning('Žádn<PERSON> log soubory nenalezeny')}")
        return []
    
    print(f"\n📊 Celkem načteno {len(recent_logs)} řádků logů")
    return recent_logs

def analyze_trading_activity(logs):
    """Analýza obchodní aktivity"""
    print(f"\n📈 {highlight('ANALÝZA OBCHODNÍ AKTIVITY')}:")
    print("=" * 60)
    
    # Vzory pro různé akce
    patterns = {
        'buy_signals': r'BUY.*RSI.*(\d+\.\d+)',
        'sell_signals': r'SELL.*RSI.*(\d+\.\d+)',
        'hold_signals': r'HOLD.*RSI.*(\d+\.\d+)',
        'profit_taking': r'PROFIT TAKING.*RSI.*(\d+\.\d+)',
        'stop_loss': r'STOP LOSS.*RSI.*(\d+\.\d+)',
        'emergency_sell': r'EMERGENCY SELL',
        'triple_buy': r'TRIPLE BUY.*RSI.*(\d+\.\d+)',
        'extreme_buy': r'EXTREME BUY.*RSI.*(\d+\.\d+)',
        'balance_changes': r'Balance.*(\d+\.\d+).*USDT',
        'pnl_changes': r'P&L.*([+-]\d+\.\d+).*USDT'
    }
    
    results = {}
    
    for pattern_name, pattern in patterns.items():
        matches = []
        for log_line in logs:
            match = re.search(pattern, log_line, re.IGNORECASE)
            if match:
                matches.append({
                    'line': log_line.strip(),
                    'value': match.group(1) if match.groups() else None,
                    'timestamp': extract_timestamp(log_line)
                })
        results[pattern_name] = matches
    
    # Výpis výsledků
    for pattern_name, matches in results.items():
        if matches:
            count = len(matches)
            pattern_display = pattern_name.replace('_', ' ').title()
            
            if 'buy' in pattern_name.lower():
                print(f"🚀 {success(pattern_display)}: {count} případů")
            elif 'sell' in pattern_name.lower() or 'stop' in pattern_name.lower():
                print(f"❌ {error(pattern_display)}: {count} případů")
            elif 'profit' in pattern_name.lower():
                print(f"💰 {success(pattern_display)}: {count} případů")
            else:
                print(f"📊 {highlight(pattern_display)}: {count} případů")
            
            # Ukázka posledních 3 případů
            for match in matches[-3:]:
                timestamp = match['timestamp'] or 'N/A'
                value = f" (RSI: {match['value']})" if match['value'] else ""
                print(f"   📝 {timestamp}: {match['line'][:80]}...{value}")
    
    return results

def analyze_rsi_levels(logs):
    """Analýza RSI úrovní"""
    print(f"\n📊 {highlight('ANALÝZA RSI ÚROVNÍ')}:")
    print("=" * 60)
    
    rsi_pattern = r'RSI.*?(\d+\.\d+)'
    rsi_values = []
    
    for log_line in logs:
        matches = re.findall(rsi_pattern, log_line)
        for match in matches:
            try:
                rsi_val = float(match)
                if 0 <= rsi_val <= 100:  # Validní RSI hodnota
                    rsi_values.append(rsi_val)
            except:
                continue
    
    if rsi_values:
        print(f"📈 Nalezeno {len(rsi_values)} RSI hodnot")
        
        # Statistiky
        min_rsi = min(rsi_values)
        max_rsi = max(rsi_values)
        avg_rsi = sum(rsi_values) / len(rsi_values)
        
        print(f"   📊 Min RSI: {min_rsi:.2f}")
        print(f"   📊 Max RSI: {max_rsi:.2f}")
        print(f"   📊 Průměr RSI: {avg_rsi:.2f}")
        
        # Kategorizace podle nových parametrů
        oversold_30 = len([r for r in rsi_values if r <= 30])
        overbought_60 = len([r for r in rsi_values if r >= 60])
        neutral = len([r for r in rsi_values if 30 < r < 60])
        extreme_oversold = len([r for r in rsi_values if r <= 20])
        profit_zone = len([r for r in rsi_values if r >= 55])
        
        print(f"\n📊 {highlight('DISTRIBUCE RSI (nové parametry)')}:")
        print(f"   🚀 Oversold (≤30): {success(f'{oversold_30} případů ({oversold_30/len(rsi_values)*100:.1f}%)')}")
        print(f"   🔥 Extreme Oversold (≤20): {success(f'{extreme_oversold} případů')}")
        print(f"   ⚖️ Neutral (30-60): {warning(f'{neutral} případů ({neutral/len(rsi_values)*100:.1f}%)')}")
        print(f"   💰 Profit Zone (≥55): {highlight(f'{profit_zone} případů')}")
        print(f"   ❌ Overbought (≥60): {error(f'{overbought_60} případů ({overbought_60/len(rsi_values)*100:.1f}%)')}")
        
        return {
            'total': len(rsi_values),
            'oversold_30': oversold_30,
            'overbought_60': overbought_60,
            'neutral': neutral,
            'extreme_oversold': extreme_oversold,
            'profit_zone': profit_zone,
            'min': min_rsi,
            'max': max_rsi,
            'avg': avg_rsi
        }
    else:
        print(f"⚠️ {warning('Žádné RSI hodnoty nenalezeny v logách')}")
        return None

def analyze_balance_changes(logs):
    """Analýza změn balance"""
    print(f"\n💰 {highlight('ANALÝZA ZMĚN BALANCE')}:")
    print("=" * 60)
    
    balance_pattern = r'Balance.*?(\d+\.\d+).*USDT'
    pnl_pattern = r'P&L.*?([+-]?\d+\.\d+).*USDT'
    
    balances = []
    pnls = []
    
    for log_line in logs:
        # Balance
        balance_match = re.search(balance_pattern, log_line)
        if balance_match:
            try:
                balance = float(balance_match.group(1))
                timestamp = extract_timestamp(log_line)
                balances.append({'value': balance, 'timestamp': timestamp, 'line': log_line.strip()})
            except:
                continue
        
        # P&L
        pnl_match = re.search(pnl_pattern, log_line)
        if pnl_match:
            try:
                pnl = float(pnl_match.group(1))
                timestamp = extract_timestamp(log_line)
                pnls.append({'value': pnl, 'timestamp': timestamp, 'line': log_line.strip()})
            except:
                continue
    
    if balances:
        print(f"📊 Nalezeno {len(balances)} záznamů balance")
        
        # Posledních 5 balance záznamů
        recent_balances = balances[-5:]
        print(f"\n📈 Posledních 5 balance záznamů:")
        for bal in recent_balances:
            timestamp = bal['timestamp'] or 'N/A'
            color_func = success if bal['value'] > 13000 else warning if bal['value'] > 12000 else error
            print(f"   💰 {timestamp}: {color_func(f'{bal[\"value\"]:.2f} USDT')}")
        
        # Trend
        if len(balances) >= 2:
            first_balance = balances[0]['value']
            last_balance = balances[-1]['value']
            change = last_balance - first_balance
            change_pct = (change / first_balance) * 100
            
            trend_color = success if change > 0 else error if change < 0 else warning
            print(f"\n📊 Trend balance:")
            print(f"   📈 První: {first_balance:.2f} USDT")
            print(f"   📈 Poslední: {last_balance:.2f} USDT")
            print(f"   📊 Změna: {trend_color(f'{change:+.2f} USDT ({change_pct:+.2f}%)')}")
    
    if pnls:
        print(f"\n💸 Nalezeno {len(pnls)} P&L záznamů")
        
        # Statistiky P&L
        positive_pnl = [p['value'] for p in pnls if p['value'] > 0]
        negative_pnl = [p['value'] for p in pnls if p['value'] < 0]
        
        if positive_pnl:
            print(f"   ✅ Pozitivní P&L: {len(positive_pnl)} případů, průměr: {success(f'+{sum(positive_pnl)/len(positive_pnl):.2f} USDT')}")
        if negative_pnl:
            print(f"   ❌ Negativní P&L: {len(negative_pnl)} případů, průměr: {error(f'{sum(negative_pnl)/len(negative_pnl):.2f} USDT')}")

def extract_timestamp(log_line):
    """Extrakce timestamp z log řádku"""
    timestamp_pattern = r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})'
    match = re.search(timestamp_pattern, log_line)
    return match.group(1) if match else None

def analyze_new_features(logs):
    """Analýza nových funkcí"""
    print(f"\n🆕 {highlight('ANALÝZA NOVÝCH FUNKCÍ')}:")
    print("=" * 60)
    
    new_features = {
        'profit_taking_rsi55': r'PROFIT TAKING.*RSI.*5[5-9]',
        'stop_loss_rsi25': r'STOP LOSS.*RSI.*2[0-5]',
        'triple_buy': r'TRIPLE BUY',
        'extreme_buy': r'EXTREME BUY.*RSI.*[12]\d',
        'emergency_sell': r'EMERGENCY SELL',
        'debug_pnl': r'DEBUG P&L',
        'startup_analysis': r'STARTUP ANALÝZA',
        'strategy_change': r'ZMĚNA STRATEGIE'
    }
    
    feature_counts = {}
    
    for feature_name, pattern in new_features.items():
        count = 0
        examples = []
        
        for log_line in logs:
            if re.search(pattern, log_line, re.IGNORECASE):
                count += 1
                if len(examples) < 2:  # Max 2 příklady
                    examples.append(log_line.strip())
        
        feature_counts[feature_name] = {'count': count, 'examples': examples}
    
    # Výpis výsledků
    for feature_name, data in feature_counts.items():
        if data['count'] > 0:
            feature_display = feature_name.replace('_', ' ').title()
            print(f"✅ {success(feature_display)}: {data['count']} případů")
            
            for example in data['examples']:
                print(f"   📝 {example[:100]}...")
        else:
            feature_display = feature_name.replace('_', ' ').title()
            print(f"⚠️ {warning(feature_display)}: Nenalezeno")
    
    return feature_counts

def main():
    """Hlavní funkce"""
    print("🔍 KONTROLA LOGŮ PO IMPLEMENTACI RSI 30/60")
    print("=" * 70)
    
    # Čtení logů
    logs = read_recent_logs()
    
    if not logs:
        print(f"\n❌ {error('Žádné logy k analýze')}")
        print(f"💡 Možné příčiny:")
        print(f"   • Bot ještě neběžel s novými parametry")
        print(f"   • Log soubory jsou v jiném umístění")
        print(f"   • Bot není spuštěný")
        return
    
    # Analýzy
    trading_activity = analyze_trading_activity(logs)
    rsi_analysis = analyze_rsi_levels(logs)
    analyze_balance_changes(logs)
    new_features = analyze_new_features(logs)
    
    # Souhrn
    print(f"\n🎯 {highlight('SOUHRN ANALÝZY')}:")
    print("=" * 60)
    
    if rsi_analysis:
        print(f"📊 RSI analýza:")
        print(f"   • Průměr RSI: {rsi_analysis['avg']:.1f}")
        print(f"   • Oversold (≤30): {rsi_analysis['oversold_30']} případů")
        print(f"   • Overbought (≥60): {rsi_analysis['overbought_60']} případů")
        print(f"   • Profit zone (≥55): {rsi_analysis['profit_zone']} případů")
    
    # Kontrola nových funkcí
    working_features = sum(1 for f in new_features.values() if f['count'] > 0)
    total_features = len(new_features)
    
    print(f"\n🆕 Nové funkce:")
    print(f"   • Funguje: {working_features}/{total_features} funkcí")
    
    if working_features >= total_features * 0.7:
        print(f"   ✅ {success('Většina nových funkcí funguje!')}")
    elif working_features >= total_features * 0.5:
        print(f"   ⚠️ {warning('Některé funkce možná nefungují')}")
    else:
        print(f"   ❌ {error('Mnoho funkcí nefunguje - zkontrolujte implementaci')}")
    
    print(f"\n💡 {highlight('DOPORUČENÍ')}:")
    if not logs:
        print(f"   🚀 Spusťte bota s novými parametry")
    elif rsi_analysis and rsi_analysis['oversold_30'] == 0:
        print(f"   📊 Žádné oversold signály - možná klidný trh")
    elif working_features < total_features * 0.5:
        print(f"   🔧 Zkontrolujte implementaci nových funkcí")
    else:
        print(f"   ✅ Systém funguje správně!")

if __name__ == "__main__":
    main()
