from pycares.errno import (
    ARES_EADDRGETNETW<PERSON><PERSON><PERSON>RA<PERSON>,
    ARES_EBADFAMILY,
    ARES_EBADFLAGS,
    ARES_EBADHINTS,
    ARES_EBADNAME,
    ARES_EBADQUERY,
    ARES_EBADRESP,
    ARES_EBADSTR,
    ARES_ECANCELLED,
    ARES_ECONNREFUSED,
    ARES_EDESTRUCTION,
    ARES_EFILE,
    ARES_EFORMERR,
    ARES_ELOADIPHLPAPI,
    ARES_ENODATA,
    ARES_ENOMEM,
    ARES_ENONAME,
    ARES_ENOTFOUND,
    ARES_ENOTIMP,
    ARES_ENOTINITIALIZED,
    ARES_EOF,
    ARES_EREFUSED,
    ARES_ESERVFAIL,
    ARES_ESERVICE,
    ARES_ETIMEOUT,
    ARES_SUCCESS,
)

__all__ = [
    'ARES_EADDRGETNETWORKPARAMS',
    'ARES_EBADFAMILY',
    'ARES_EBADFLAGS',
    'ARES_EBADHINTS',
    'ARES_EBADNAME',
    'ARES_EBADQUERY',
    'ARES_EBADRESP',
    'ARES_EBADSTR',
    'ARES_ECANCELLED',
    'ARES_ECONNREFUSED',
    'ARES_EDESTRUCTION',
    'ARES_EFILE',
    'ARES_EFORMERR',
    'ARES_ELOADIPHLPAPI',
    'ARES_ENODATA',
    'ARES_ENOMEM',
    'ARES_ENONAME',
    'ARES_ENOTFOUND',
    'ARES_ENOTIMP',
    'ARES_ENOTINITIALIZED',
    'ARES_EOF',
    'ARES_EREFUSED',
    'ARES_ESERVFAIL',
    'ARES_ESERVICE',
    'ARES_ETIMEOUT',
    'ARES_SUCCESS',
    'DNSError',
]


class DNSError(Exception):
    """Base class for all DNS errors."""
