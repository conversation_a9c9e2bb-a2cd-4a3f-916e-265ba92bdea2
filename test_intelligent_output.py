#!/usr/bin/env python3
"""
🎯 TEST INTELIGENTNÍHO VÝPISU
Testování nové funkce výpisu pouze při změnách
"""

import time
from bot import TradingBot
from colors import print_banner, success, error, highlight, warning

def simulate_trading_cycles():
    """Simulace trading cyklů pro test inteligentního výpisu"""
    print_banner("🎯 TEST INTELIGENTNÍHO VÝPISU")
    
    try:
        # Vytvoření bot instance
        bot = TradingBot()
        
        # Simulace různých scénářů
        test_scenarios = [
            {
                'name': 'Neměnné hodnoty',
                'cycles': 5,
                'balance': 15000.0,
                'positions': 2,
                'rsi_values': {'BTC/USDT': 50.0, 'ETH/USDT': 45.0, 'ADA/USDT': 55.0},
                'actions': {'BTC/USDT': 'HOLD', 'ETH/USDT': 'HOLD', 'ADA/USDT': 'HOLD'}
            },
            {
                'name': 'Změna balance',
                'cycles': 3,
                'balance': 15050.0,  # Změna +50 USDT
                'positions': 2,
                'rsi_values': {'BTC/USDT': 50.0, 'ETH/USDT': 45.0, 'ADA/USDT': 55.0},
                'actions': {'BTC/USDT': 'HOLD', 'ETH/USDT': 'HOLD', 'ADA/USDT': 'HOLD'}
            },
            {
                'name': 'Změna RSI',
                'cycles': 3,
                'balance': 15050.0,
                'positions': 2,
                'rsi_values': {'BTC/USDT': 35.0, 'ETH/USDT': 45.0, 'ADA/USDT': 55.0},  # BTC RSI změna
                'actions': {'BTC/USDT': 'BUY', 'ETH/USDT': 'HOLD', 'ADA/USDT': 'HOLD'}  # BTC signál změna
            },
            {
                'name': 'Změna pozic',
                'cycles': 3,
                'balance': 15050.0,
                'positions': 3,  # Nová pozice
                'rsi_values': {'BTC/USDT': 35.0, 'ETH/USDT': 45.0, 'ADA/USDT': 55.0},
                'actions': {'BTC/USDT': 'BUY', 'ETH/USDT': 'HOLD', 'ADA/USDT': 'HOLD'}
            },
            {
                'name': 'Dlouhé neměnné období',
                'cycles': 15,  # Více než 10 cyklů
                'balance': 15050.0,
                'positions': 3,
                'rsi_values': {'BTC/USDT': 50.0, 'ETH/USDT': 45.0, 'ADA/USDT': 55.0},
                'actions': {'BTC/USDT': 'HOLD', 'ETH/USDT': 'HOLD', 'ADA/USDT': 'HOLD'}
            }
        ]
        
        print(f"\n🧪 {highlight('SPOUŠTÍM SIMULACI SCÉNÁŘŮ')}:")
        
        for scenario in test_scenarios:
            print(f"\n📊 {highlight(f'SCÉNÁŘ: {scenario[\"name\"]}')}")
            print(f"   🔄 Cyklů: {scenario['cycles']}")
            print(f"   💰 Balance: {scenario['balance']} USDT")
            print(f"   📈 Pozice: {scenario['positions']}")
            
            # Simulace cyklů
            for cycle in range(scenario['cycles']):
                print(f"\n--- Cyklus {cycle + 1} ---")
                
                # Test pro každý symbol
                for symbol in ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']:
                    rsi = scenario['rsi_values'][symbol]
                    action = scenario['actions'][symbol]
                    balance = scenario['balance']
                    positions = scenario['positions']
                    
                    # Test detekce změn
                    has_change = bot.has_significant_change(symbol, rsi, action, balance, positions)
                    should_show = bot.should_show_summary()
                    
                    if has_change:
                        print(f"   ✅ {symbol}: ZMĚNA DETEKOVÁNA - RSI {rsi}, {action}")
                    elif should_show:
                        print(f"   📊 {symbol}: SOUHRN (cyklus {bot.unchanged_cycles}) - RSI {rsi}, {action}")
                    else:
                        print(f"   ⏸️ {symbol}: Bez výpisu (cyklus {bot.unchanged_cycles})")
                
                # Krátká pauza mezi cykly
                time.sleep(0.1)
            
            # Reset pro další scénář
            bot.last_balance = None
            bot.last_positions_count = None
            bot.last_signals = {}
            bot.last_rsi_values = {}
            bot.unchanged_cycles = 0
            
            print(f"   ✅ Scénář '{scenario['name']}' dokončen")
        
        print(f"\n🎉 {success('VŠECHNY SCÉNÁŘE DOKONČENY!')}")
        return True
        
    except Exception as e:
        print(error(f"❌ Chyba při simulaci: {e}"))
        import traceback
        traceback.print_exc()
        return False

def show_intelligent_output_benefits():
    """Výhody inteligentního výpisu"""
    print(f"\n🎯 {highlight('VÝHODY INTELIGENTNÍHO VÝPISU')}:")
    print("=" * 60)
    
    benefits = [
        "📺 Čistší výstup - žádné opakování stejných hodnot",
        "🎯 Výpis pouze při významných změnách",
        "📊 Balance se zobrazí jen při změně > 0.01 USDT",
        "📈 RSI se zobrazí jen při změně > 2.0 bodů",
        "🔄 Signály se zobrazí jen při změně akce",
        "📋 Pozice se zobrazí jen při změně počtu",
        "⏰ Souhrn každých 10 neměnných cyklů",
        "🎭 Různé zprávy pro monitoring vs. aktivitu"
    ]
    
    for benefit in benefits:
        print(f"   ✅ {benefit}")

def show_output_logic():
    """Logika inteligentního výpisu"""
    print(f"\n🧠 {highlight('LOGIKA INTELIGENTNÍHO VÝPISU')}:")
    print("=" * 60)
    
    logic_rules = {
        "📊 VÝPIS SIGNÁLU": [
            "Změna akce (BUY → HOLD → SELL)",
            "Změna RSI > 2.0 bodů",
            "Každých 10 neměnných cyklů"
        ],
        "💰 VÝPIS BALANCE": [
            "Změna balance > 0.01 USDT",
            "Změna počtu pozic",
            "Každých 10 neměnných cyklů"
        ],
        "🎭 AKTIVITA SOUHRN": [
            "Obchody provedeny → vždy zobrazit",
            "Pozice sledovány → při změně nebo každých 10 cyklů",
            "Žádná aktivita → každých 5 cyklů"
        ],
        "⏰ MONITORING ZPRÁVY": [
            "Cyklus 1-4: Bez výpisu",
            "Cyklus 5: 'Žádná aktivita'",
            "Cyklus 6-9: 'Monitoring trhu...'",
            "Cyklus 10: Souhrn všech hodnot"
        ]
    }
    
    for category, rules in logic_rules.items():
        print(f"\n{category}:")
        for rule in rules:
            print(f"   • {rule}")

def show_before_after_comparison():
    """Porovnání před a po implementaci"""
    print(f"\n📊 {highlight('POROVNÁNÍ PŘED × PO')}:")
    print("=" * 60)
    
    print(f"\n❌ {error('PŘED (opakující se výpis)')}:")
    print("""
⏸️ BTC/USDT | RSI: 50.00 | HOLD | Neutrální zóna
💎 Balance: 15000.00 USDT | P&L: 0.00 USDT
⏸️ BTC/USDT | RSI: 50.00 | HOLD | Neutrální zóna  
💎 Balance: 15000.00 USDT | P&L: 0.00 USDT
⏸️ BTC/USDT | RSI: 50.00 | HOLD | Neutrální zóna
💎 Balance: 15000.00 USDT | P&L: 0.00 USDT
    """)
    
    print(f"\n✅ {success('PO (inteligentní výpis)')}:")
    print("""
⏸️ BTC/USDT | RSI: 50.00 | HOLD | Neutrální zóna
💎 Balance: 15000.00 USDT | P&L: 0.00 USDT
Monitoring trhu... (cyklus 2)
Monitoring trhu... (cyklus 3)
Monitoring trhu... (cyklus 4)
⏸️ Žádná aktivita - čekání na signály (cyklus 5)
    """)

def main():
    """Hlavní funkce"""
    print("🎯 TEST INTELIGENTNÍHO VÝPISU TRADING BOTA")
    print("=" * 70)
    
    # Test simulace
    if simulate_trading_cycles():
        show_intelligent_output_benefits()
        show_output_logic()
        show_before_after_comparison()
        
        print(f"\n🚀 {highlight('INTELIGENTNÍ VÝPIS AKTIVNÍ!')}")
        print(f"   📺 Čistší a přehlednější výstup")
        print(f"   🎯 Výpis pouze při změnách")
        print(f"   ⏰ Souhrn každých 10 cyklů")
        print(f"   🎭 Různé zprávy pro různé situace")
        
        print(f"\n💡 {highlight('SPUŠTĚNÍ S INTELIGENTNÍM VÝPISEM')}:")
        print(f"python main.py --mode live")
        
    else:
        print(f"\n❌ {error('NĚKTERÉ TESTY SELHALY')}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
