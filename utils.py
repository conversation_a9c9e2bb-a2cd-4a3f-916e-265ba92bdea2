import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List
import json
from datetime import datetime

def format_currency(amount: float, currency: str = "USDT") -> str:
    """Formátování měny"""
    return f"{amount:,.2f} {currency}"

def format_percentage(value: float) -> str:
    """Formátování procent"""
    return f"{value:+.2f}%"

def calculate_position_value(quantity: float, price: float) -> float:
    """Výpočet hodnoty pozice"""
    return quantity * price

def save_backtest_results(results: Dict, filename: str = None):
    """Uložení výsledků backtestingu"""
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"backtest_results_{timestamp}.json"
    
    # Převod pandas objektů na serializovatelné formáty
    serializable_results = {}
    for key, value in results.items():
        if isinstance(value, (pd.Timestamp, datetime)):
            serializable_results[key] = value.isoformat()
        elif isinstance(value, np.ndarray):
            serializable_results[key] = value.tolist()
        elif isinstance(value, (np.integer, np.floating)):
            serializable_results[key] = float(value)
        elif key in ['trades', 'equity_curve'] and isinstance(value, list):
            # Speciální zpracování pro trades a equity_curve
            processed_list = []
            for item in value:
                processed_item = {}
                for k, v in item.items():
                    if isinstance(v, (pd.Timestamp, datetime)):
                        processed_item[k] = v.isoformat()
                    elif isinstance(v, (np.integer, np.floating)):
                        processed_item[k] = float(v)
                    else:
                        processed_item[k] = v
                processed_list.append(processed_item)
            serializable_results[key] = processed_list
        else:
            serializable_results[key] = value
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print(f"Výsledky uloženy do: {filename}")

def print_backtest_summary(results: Dict):
    """Výpis souhrnu backtestingu"""
    if 'error' in results:
        print(f"Chyba: {results['error']}")
        return
    
    print("\n" + "="*50)
    print("           BACKTEST VÝSLEDKY")
    print("="*50)
    
    print(f"Celkový počet obchodů: {results['total_trades']}")
    print(f"Vítězné obchody: {results['winning_trades']}")
    print(f"Ztrátové obchody: {results['losing_trades']}")
    print(f"Win Rate: {format_percentage(results['win_rate'])}")
    print()
    
    print(f"Celkový P&L: {format_currency(results['total_pnl'])}")
    print(f"Celkový výnos: {format_percentage(results['total_return'])}")
    print(f"Průměrný zisk: {format_currency(results['avg_win'])}")
    print(f"Průměrná ztráta: {format_currency(results['avg_loss'])}")
    print(f"Profit Factor: {results['profit_factor']:.2f}")
    print()
    
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    print(f"Maximum Drawdown: {format_percentage(results['max_drawdown'])}")
    print(f"Finální zůstatek: {format_currency(results['final_balance'])}")
    print("="*50)

def plot_backtest_results(results: Dict, symbol: str, save_plot: bool = True):
    """Vytvoření grafů z backtestingu"""
    if 'error' in results:
        print(f"Nelze vytvořit graf: {results['error']}")
        return
    
    equity_df = pd.DataFrame(results['equity_curve'])
    trades_df = pd.DataFrame(results['trades'])
    
    # Vytvoření subplotů
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=(
            f'{symbol} - Cena a Obchody',
            'Equity Curve',
            'Drawdown'
        ),
        vertical_spacing=0.08,
        row_heights=[0.5, 0.3, 0.2]
    )
    
    # Graf 1: Cena a obchody
    fig.add_trace(
        go.Scatter(
            x=equity_df['timestamp'],
            y=equity_df['price'],
            mode='lines',
            name='Cena',
            line=dict(color='blue')
        ),
        row=1, col=1
    )
    
    # Označení nákupů
    if not trades_df.empty:
        buy_trades = trades_df
        fig.add_trace(
            go.Scatter(
                x=buy_trades['entry_time'],
                y=buy_trades['entry_price'],
                mode='markers',
                name='Nákup',
                marker=dict(color='green', size=8, symbol='triangle-up')
            ),
            row=1, col=1
        )
        
        # Označení prodejů
        fig.add_trace(
            go.Scatter(
                x=buy_trades['exit_time'],
                y=buy_trades['exit_price'],
                mode='markers',
                name='Prodej',
                marker=dict(color='red', size=8, symbol='triangle-down')
            ),
            row=1, col=1
        )
    
    # Graf 2: Equity curve
    fig.add_trace(
        go.Scatter(
            x=equity_df['timestamp'],
            y=equity_df['total_value'],
            mode='lines',
            name='Portfolio Value',
            line=dict(color='green')
        ),
        row=2, col=1
    )
    
    # Graf 3: Drawdown
    equity_df['peak'] = equity_df['total_value'].cummax()
    equity_df['drawdown'] = (equity_df['total_value'] - equity_df['peak']) / equity_df['peak'] * 100
    
    fig.add_trace(
        go.Scatter(
            x=equity_df['timestamp'],
            y=equity_df['drawdown'],
            mode='lines',
            name='Drawdown',
            line=dict(color='red'),
            fill='tonexty'
        ),
        row=3, col=1
    )
    
    # Nastavení layoutu
    fig.update_layout(
        title=f'Backtest Results - {symbol}',
        height=800,
        showlegend=True
    )
    
    fig.update_xaxes(title_text="Datum", row=3, col=1)
    fig.update_yaxes(title_text="Cena", row=1, col=1)
    fig.update_yaxes(title_text="Hodnota portfolia", row=2, col=1)
    fig.update_yaxes(title_text="Drawdown (%)", row=3, col=1)
    
    if save_plot:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"backtest_chart_{symbol.replace('/', '_')}_{timestamp}.html"
        fig.write_html(filename)
        print(f"Graf uložen jako: {filename}")
    
    fig.show()

def create_trades_report(trades: List[Dict]) -> pd.DataFrame:
    """Vytvoření detailního reportu obchodů"""
    if not trades:
        return pd.DataFrame()
    
    df = pd.DataFrame(trades)
    
    # Přidání dalších metrik
    df['duration'] = (pd.to_datetime(df['exit_time']) - pd.to_datetime(df['entry_time'])).dt.total_seconds() / 3600  # hodiny
    df['return_pct'] = df['pnl'] / (df['entry_price'] * df['quantity']) * 100
    
    return df

def analyze_trade_performance(trades: List[Dict]) -> Dict:
    """Analýza výkonnosti obchodů"""
    if not trades:
        return {}
    
    df = create_trades_report(trades)
    
    analysis = {
        'best_trade': df.loc[df['pnl'].idxmax()].to_dict() if not df.empty else None,
        'worst_trade': df.loc[df['pnl'].idxmin()].to_dict() if not df.empty else None,
        'avg_trade_duration': df['duration'].mean(),
        'median_return': df['return_pct'].median(),
        'std_return': df['return_pct'].std(),
        'consecutive_wins': 0,
        'consecutive_losses': 0
    }
    
    # Výpočet consecutive wins/losses
    df['win'] = df['pnl'] > 0
    current_streak = 0
    max_win_streak = 0
    max_loss_streak = 0
    
    for win in df['win']:
        if win:
            if current_streak >= 0:
                current_streak += 1
            else:
                current_streak = 1
            max_win_streak = max(max_win_streak, current_streak)
        else:
            if current_streak <= 0:
                current_streak -= 1
            else:
                current_streak = -1
            max_loss_streak = max(max_loss_streak, abs(current_streak))
    
    analysis['consecutive_wins'] = max_win_streak
    analysis['consecutive_losses'] = max_loss_streak
    
    return analysis
