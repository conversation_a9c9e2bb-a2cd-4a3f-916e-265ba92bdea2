#!/usr/bin/env python3
"""
Rychlý test trading bota
Ověří základní funkčnost bez nutnosti API klíčů
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Přidání aktuálního adresáře do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test importů"""
    print("🔍 Testování importů...")
    
    try:
        import ccxt
        print("✅ ccxt - OK")
    except ImportError:
        print("❌ ccxt - CHYBÍ")
        return False
    
    try:
        import pandas
        print("✅ pandas - OK")
    except ImportError:
        print("❌ pandas - CHYBÍ")
        return False
    
    try:
        import ta
        print("✅ ta - OK")
    except ImportError:
        print("❌ ta - CHYBÍ")
        return False
    
    try:
        from strategy import RSIStrategy
        print("✅ RSIStrategy - OK")
    except ImportError as e:
        print(f"❌ RSIStrategy - CHYBA: {e}")
        return False
    
    try:
        from risk_management import RiskManager
        print("✅ RiskManager - OK")
    except ImportError as e:
        print(f"❌ RiskManager - CHYBA: {e}")
        return False
    
    try:
        from backtesting import Backtester
        print("✅ Backtester - OK")
    except ImportError as e:
        print(f"❌ Backtester - CHYBA: {e}")
        return False
    
    return True

def test_strategy():
    """Test RSI strategie"""
    print("\n📊 Testování RSI strategie...")
    
    try:
        from strategy import RSIStrategy
        
        # Vytvoření testovacích dat
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1H')
        prices = 50000 + np.cumsum(np.random.randn(50) * 100)  # Simulace BTC ceny
        
        test_data = pd.DataFrame({
            'open': prices * 0.999,
            'high': prices * 1.002,
            'low': prices * 0.998,
            'close': prices,
            'volume': np.random.randint(100, 1000, 50)
        }, index=dates)
        
        # Test strategie
        strategy = RSIStrategy(rsi_period=14, oversold=30, overbought=70)
        signal = strategy.analyze_signal('BTC/USDT', test_data)
        
        print(f"✅ RSI: {signal.get('rsi', 'N/A'):.2f}")
        print(f"✅ Akce: {signal.get('action', 'N/A')}")
        print(f"✅ Důvod: {signal.get('reason', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování strategie: {e}")
        return False

def test_risk_management():
    """Test risk managementu"""
    print("\n🛡️ Testování risk managementu...")
    
    try:
        from risk_management import RiskManager
        
        rm = RiskManager(max_position_size=0.2, stop_loss_pct=3.0, take_profit_pct=5.0)
        
        # Test výpočtu velikosti pozice
        balance = 10000
        price = 50000
        position_size = rm.calculate_position_size(balance, price, 'BTC/USDT')
        
        print(f"✅ Velikost pozice: {position_size:.6f} BTC")
        print(f"✅ Hodnota pozice: {position_size * price:.2f} USDT")
        
        # Test validace obchodu
        validation = rm.validate_trade('BTC/USDT', 'buy', position_size, price, balance)
        print(f"✅ Validace: {validation['valid']} - {validation['reason']}")
        
        # Test SL/TP
        sl_tp = rm.calculate_stop_loss_take_profit(price, 'buy')
        print(f"✅ Stop Loss: {sl_tp['stop_loss']:.2f}")
        print(f"✅ Take Profit: {sl_tp['take_profit']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování risk managementu: {e}")
        return False

def test_config():
    """Test konfigurace"""
    print("\n⚙️ Testování konfigurace...")
    
    try:
        from config import Config
        
        print(f"✅ Burza: {Config.EXCHANGE}")
        print(f"✅ Symboly: {Config.SYMBOLS}")
        print(f"✅ Timeframe: {Config.TIMEFRAME}")
        print(f"✅ RSI parametry: {Config.RSI_PERIOD}/{Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT}")
        print(f"✅ Max pozice: {Config.MAX_POSITION_SIZE * 100}%")
        print(f"✅ Stop Loss: {Config.STOP_LOSS_PERCENT}%")
        print(f"✅ Take Profit: {Config.TAKE_PROFIT_PERCENT}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování konfigurace: {e}")
        return False

def test_logger():
    """Test loggeru"""
    print("\n📝 Testování loggeru...")
    
    try:
        from logger import logger
        
        logger.info("Test info zpráva")
        logger.warning("Test warning zpráva")
        logger.trade_log("BUY", "BTC/USDT", 50000, 0.001, "Test obchod")
        logger.balance_log(10000, 100)
        
        print("✅ Logger funguje správně")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování loggeru: {e}")
        return False

def main():
    """Hlavní funkce"""
    print("🤖 TRADING BOT - RYCHLÝ TEST")
    print("=" * 40)
    
    tests = [
        ("Importy", test_imports),
        ("Konfigurace", test_config),
        ("Logger", test_logger),
        ("RSI Strategie", test_strategy),
        ("Risk Management", test_risk_management),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ Test {test_name} selhal")
        except Exception as e:
            print(f"❌ Test {test_name} selhal s chybou: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 VÝSLEDKY: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy prošly! Bot je připraven k použití.")
        print("\n📋 Další kroky:")
        print("1. Nastavte API klíče v .env souboru")
        print("2. Spusťte: python main.py --mode test")
        print("3. Spusťte backtesting: python main.py --mode backtest")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte instalaci závislostí:")
        print("   pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
