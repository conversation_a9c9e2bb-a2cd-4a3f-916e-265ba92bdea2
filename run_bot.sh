#!/bin/bash

# Trading Bot - Spouštěcí skript pro Linux/Mac
# Autor: Trading Bot System

# Nastavení barev
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Banner
echo ""
echo "============================================================"
echo "🤖 TRADING BOT - RSI STRATEGIE"
echo "============================================================"
echo "Automatický trading bot pro futures obchodování"
echo "Podporuje Binance a Bybit (demo i live účty)"
echo "============================================================"
echo ""

# Kontrola Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 není nainstalován!${NC}"
    echo -e "${YELLOW}📦 Nainstalujte Python3:${NC}"
    echo "   Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "   CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "   macOS: brew install python3"
    exit 1
fi

# Kontrola pip
if ! command -v pip3 &> /dev/null; then
    echo -e "${RED}❌ pip3 není nainstalován!${NC}"
    echo -e "${YELLOW}📦 Nainstalujte pip3${NC}"
    exit 1
fi

# Kontrola .env souboru
if [ ! -f .env ]; then
    echo -e "${RED}❌ Soubor .env neexistuje!${NC}"
    echo -e "${YELLOW}📋 Zkopírujte .env.example jako .env a vyplňte své API klíče:${NC}"
    echo "   cp .env.example .env"
    echo "   # Poté upravte .env soubor"
    exit 1
fi

# Kontrola virtuálního prostředí
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}📦 Vytvářím virtuální prostředí...${NC}"
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Nepodařilo se vytvořit virtuální prostředí${NC}"
        exit 1
    fi
fi

# Aktivace virtuálního prostředí
echo -e "${BLUE}🔧 Aktivuji virtuální prostředí...${NC}"
source venv/bin/activate

# Instalace závislostí
if [ ! -f "venv/.dependencies_installed" ]; then
    echo -e "${YELLOW}📦 Instaluji závislosti...${NC}"
    pip install -r requirements.txt
    if [ $? -eq 0 ]; then
        touch venv/.dependencies_installed
        echo -e "${GREEN}✅ Závislosti nainstalovány${NC}"
    else
        echo -e "${RED}❌ Chyba při instalaci závislostí${NC}"
        exit 1
    fi
fi

# Spuštění bota
echo -e "${GREEN}🚀 Spouštím Trading Bot...${NC}"
python run_bot.py

# Deaktivace virtuálního prostředí
deactivate

echo ""
echo -e "${BLUE}👋 Program ukončen${NC}"
