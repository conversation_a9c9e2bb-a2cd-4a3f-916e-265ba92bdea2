#!/usr/bin/env python3
"""
Test nové RSI strategie s úrovněmi 35/65
"""

from demo_mode import DemoTrader
from colors import print_banner, success, highlight, profit, loss

def test_new_strategy():
    """Test nové strategie"""
    print_banner("🧪 TEST NOVÉ RSI STRATEGIE (35/65) 🧪")
    
    # Krátký test - 30 dní
    days = 30
    
    print(highlight(f"Testování {days} dní s novými RSI úrovněmi:"))
    print(success("📊 RSI Oversold: 35 (dříve 30)"))
    print(success("📊 RSI Overbought: 65 (dříve 70)"))
    print(success("🎯 Očekáváme více obchodních signálů!"))
    
    # Spuštění demo traderu
    trader = DemoTrader()
    trader.run_simulation(days)

if __name__ == "__main__":
    test_new_strategy()
