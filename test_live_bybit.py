#!/usr/bin/env python3
"""
Test live Bybit API s va<PERSON><PERSON><PERSON>
"""

import ccxt
import sys
import os
from dotenv import load_dotenv

# Načtení environment variables
load_dotenv()

def test_live_bybit():
    """Test live Bybit API"""
    
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    print("🔍 Test Live Bybit API...")
    print(f"API Key: {api_key}")
    print(f"API Secret: {api_secret[:8]}...")
    
    # Konfigurace pro live Bybit
    configs = [
        {
            'name': 'Základní live',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': False,
            }
        },
        {
            'name': 'Live s unified',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': False,
                'options': {
                    'defaultType': 'unified',
                }
            }
        },
        {
            'name': 'Live s futures',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': False,
                'options': {
                    'defaultType': 'swap',
                }
            }
        }
    ]
    
    for config_info in configs:
        print(f"\n🧪 Test: {config_info['name']}")
        try:
            exchange = ccxt.bybit(config_info['config'])
            
            # Test 1: Získání balance
            print("   📊 Získávání balance...")
            balance = exchange.fetch_balance()
            
            print(f"   ✅ Balance získán!")
            print(f"   💰 USDT: {balance.get('USDT', {}).get('free', 0)}")
            
            # Výpis všech dostupných měn
            available_currencies = [k for k, v in balance.items() 
                                  if isinstance(v, dict) and v.get('free', 0) > 0]
            print(f"   💱 Dostupné měny: {available_currencies}")
            
            # Test 2: Získání ticker
            print("   📈 Získávání ticker...")
            ticker = exchange.fetch_ticker('BTC/USDT')
            print(f"   ✅ BTC/USDT: {ticker['last']}")
            
            # Test 3: Získání pozic (pro futures)
            try:
                print("   📋 Získávání pozic...")
                positions = exchange.fetch_positions()
                open_positions = [p for p in positions if p['contracts'] > 0]
                print(f"   ✅ Otevřené pozice: {len(open_positions)}")
            except Exception as e:
                print(f"   ⚠️  Pozice nedostupné: {e}")
            
            print(f"   🎉 {config_info['name']} - ÚSPĚCH!")
            return True, exchange
            
        except Exception as e:
            print(f"   ❌ {config_info['name']} - chyba: {e}")
    
    return False, None

def test_trading_permissions(exchange):
    """Test trading oprávnění"""
    print("\n🔐 Test trading oprávnění...")
    
    try:
        # Test získání order history (read oprávnění)
        print("   📜 Test čtení order history...")
        orders = exchange.fetch_orders('BTC/USDT', limit=1)
        print("   ✅ Čtení orders - OK")
        
        # Test market data
        print("   📊 Test market data...")
        orderbook = exchange.fetch_order_book('BTC/USDT', 5)
        print("   ✅ Market data - OK")
        
        print("   🎉 Všechna oprávnění fungují!")
        return True
        
    except Exception as e:
        print(f"   ❌ Chyba oprávnění: {e}")
        return False

def main():
    """Hlavní funkce"""
    print("🤖 LIVE BYBIT API TEST")
    print("=" * 50)
    print("⚠️  POZOR: Testujeme LIVE API s reálným účtem!")
    print("🛡️  Nebudeme provádět žádné obchody, pouze čtení")
    print("=" * 50)
    
    # Test připojení
    success, exchange = test_live_bybit()
    
    if success:
        print("\n🎉 PŘIPOJENÍ ÚSPĚŠNÉ!")
        
        # Test oprávnění
        test_trading_permissions(exchange)
        
        print("\n" + "=" * 50)
        print("✅ VÝSLEDEK: API klíče fungují!")
        print("📋 Můžete nyní používat bota:")
        print("   1. python main.py --mode test")
        print("   2. python main.py --mode backtest") 
        print("   3. python main.py --mode live (POZOR: reálné peníze!)")
        print("\n⚠️  DŮLEŽITÉ:")
        print("   - Máte LIVE API klíče (ne testnet)")
        print("   - Bot bude obchodovat s reálnými penězi")
        print("   - Začněte s malým kapitálem")
        print("   - Sledujte bot pravidelně")
        
    else:
        print("\n❌ PŘIPOJENÍ SELHALO")
        print("🔧 Možná řešení:")
        print("1. Zkontrolujte IP whitelist na Bybit")
        print("2. Ověřte oprávnění API klíčů")
        print("3. Zkuste vytvořit nové API klíče")
        print("4. Použijte demo režim: python demo_mode.py")

if __name__ == "__main__":
    main()
