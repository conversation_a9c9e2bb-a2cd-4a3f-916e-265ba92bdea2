import ccxt
import pandas as pd
import ta  # PŘIDÁNO: Potřebné pro technické indikátory
import time
import traceback  # PŘIDÁNO: Pro lepší error handling
from datetime import datetime
from typing import Dict, List, Optional, Any  # ROZŠÍŘENO: <PERSON><PERSON><PERSON> type hints

# Lokální importy s error handling
try:
    from config import Config, BINANCE_TESTNET, BYBIT_TESTNET
    from strategy import RSIStrategy
    from risk_management import RiskManager
    from logger import logger
    from colors import (
        print_cycle_start, print_cycle_end, print_positions,
        print_banner, success, error, warning, highlight,
        print_no_activity, print_market_monitoring, format_rsi, print_strategy
    )
    from simple_notifications import notifier
    from intelligent_strategy_selector import IntelligentStrategySelector
except ImportError as e:
    print(f"❌ Chyba importu: {e}")
    print("🔧 Zkontrolujte, zda existují všechny potřebné soubory")
    import sys
    sys.exit(1)

class TradingBot:
    """Hlavní třída trading bota"""
    
    def __init__(self):
        self.config = Config()
        self.exchange = None
        self.strategy = RSIStrategy(
            rsi_period=Config.RSI_PERIOD,
            oversold=Config.RSI_OVERSOLD,
            overbought=Config.RSI_OVERBOUGHT,
            ma_period=Config.MA_PERIOD
        )
        self.risk_manager = RiskManager(
            max_position_size=Config.MAX_POSITION_SIZE,
            stop_loss_pct=Config.STOP_LOSS_PERCENT,
            take_profit_pct=Config.TAKE_PROFIT_PERCENT
        )
        self.strategy_selector = IntelligentStrategySelector()  # NOVÝ: Inteligentní výběr strategie
        self.current_strategy_name = "Enhanced RSI Strategy"  # Aktuální strategie
        self.strategy_params = {}  # Parametry aktuální strategie
        self.strategy_update_counter = 0  # Počítadlo pro update strategie
        self.running = False
        self.last_prices = {}
        
    def initialize_exchange(self):
        """Inicializace burzy"""
        try:
            if Config.EXCHANGE.lower() == 'binance':
                if Config.SANDBOX:
                    self.exchange = ccxt.binance(BINANCE_TESTNET)
                else:
                    self.exchange = ccxt.binance({
                        'apiKey': Config.API_KEY,
                        'secret': Config.API_SECRET,
                    })
            elif Config.EXCHANGE.lower() == 'bybit':
                if Config.SANDBOX:
                    self.exchange = ccxt.bybit(BYBIT_TESTNET)
                else:
                    self.exchange = ccxt.bybit({
                        'apiKey': Config.API_KEY,
                        'secret': Config.API_SECRET,
                    })
            else:
                raise ValueError(f"Nepodporovaná burza: {Config.EXCHANGE}")
            
            # Nastavení pro futures
            self.exchange.set_sandbox_mode(Config.SANDBOX)
            
            # Test připojení
            balance = self.exchange.fetch_balance()
            logger.info(f"Připojení k {Config.EXCHANGE} {'(testnet)' if Config.SANDBOX else ''} úspěšné")
            logger.info(f"Dostupný zůstatek: {balance.get('USDT', {}).get('free', 0)} USDT")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba při inicializaci burzy: {e}")
            return False
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """Získání OHLCV dat"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            return df
        except Exception as e:
            logger.error(f"Chyba při získávání dat pro {symbol}: {e}")
            return None
    
    def get_balance(self) -> float:
        """Získání dostupného zůstatku v USDT"""
        try:
            balance = self.exchange.fetch_balance()
            return float(balance.get('USDT', {}).get('free', 0))
        except Exception as e:
            logger.error(f"Chyba při získávání zůstatku: {e}")
            return 0.0

    def update_strategy_if_needed(self, symbol: str):
        """Aktualizace strategie podle tržních podmínek"""
        try:
            # Aktualizace strategie každých 10 cyklů
            self.strategy_update_counter += 1
            if self.strategy_update_counter % 10 != 0:
                return

            logger.info(f"🧠 Analýza tržních podmínek pro {symbol}...")

            # Analýza trhu
            market_analysis = self.strategy_selector.analyze_market_conditions(self.exchange, symbol)

            if market_analysis:
                # Výběr optimální strategie
                optimal_strategy = self.strategy_selector.select_optimal_strategy(market_analysis)

                if optimal_strategy:
                    new_strategy_name = optimal_strategy['name']
                    confidence = optimal_strategy['confidence']

                    # Pokud se strategie změnila
                    if new_strategy_name != self.current_strategy_name:
                        logger.info(f"🔄 ZMĚNA STRATEGIE!")
                        logger.info(f"   📊 Stará: {self.current_strategy_name}")
                        logger.info(f"   📊 Nová: {new_strategy_name}")
                        logger.info(f"   🎯 Confidence: {confidence:.1f}%")

                        # Aktualizace strategie
                        self.current_strategy_name = new_strategy_name
                        self.strategy_params = self.strategy_selector.get_strategy_parameters(optimal_strategy)

                        # Aktualizace RSI parametrů
                        if 'rsi_oversold' in self.strategy_params:
                            self.strategy.oversold = self.strategy_params['rsi_oversold']
                            self.strategy.overbought = self.strategy_params['rsi_overbought']

                            logger.info(f"   📈 Nové RSI: {self.strategy.oversold}/{self.strategy.overbought}")

                        # Discord notifikace o změně strategie
                        trend = market_analysis['trend']
                        volatility = market_analysis['volatility']

                        strategy_change_msg = (
                            f"🧠 **ZMĚNA STRATEGIE**\n"
                            f"📊 **Nová strategie:** {new_strategy_name}\n"
                            f"🎯 **Confidence:** {confidence:.1f}%\n"
                            f"📈 **Trend:** {trend['strength']} (score: {trend['score']})\n"
                            f"📊 **Volatilita:** {volatility['level']} (score: {volatility['score']})\n"
                            f"🎯 **RSI:** {self.strategy_params.get('rsi_oversold', 45)}/{self.strategy_params.get('rsi_overbought', 55)}\n"
                            f"⏰ **Timeframe:** {self.strategy_params.get('timeframe', '5m')}"
                        )

                        notifier.send_message(strategy_change_msg)

                    # Výpis aktuální strategie
                    trend = market_analysis['trend']
                    volatility = market_analysis['volatility']

                    print(f"🧠 {highlight('AKTUÁLNÍ STRATEGIE')}: {success(new_strategy_name)}")
                    print(f"   📈 Trend: {trend['strength']} (score: {trend['score']})")
                    print(f"   📊 Volatilita: {volatility['level']} (score: {volatility['score']})")
                    print(f"   🎯 Confidence: {confidence:.1f}%")
                    print(f"   📊 RSI: {self.strategy_params.get('rsi_oversold', 45)}/{self.strategy_params.get('rsi_overbought', 55)}")

        except Exception as e:
            logger.error(f"Chyba při aktualizaci strategie: {e}")
    
    def place_market_order(self, symbol: str, side: str, quantity: float) -> Optional[Dict]:
        """Zadání market orderu s inteligentním sizing"""
        try:
            # Validace obchodu
            current_price = self.last_prices.get(symbol, 0)
            balance = self.get_balance()

            validation = self.risk_manager.validate_trade(
                symbol, side, quantity, current_price, balance
            )

            if not validation['valid']:
                logger.warning(f"Obchod zamítnut: {validation['reason']}")
                return None

            # Zpracování upraveného množství
            final_quantity = quantity
            if validation.get('adjusted', False):
                final_quantity = validation['adjusted_quantity']
                original_quantity = validation['original_quantity']

                if validation.get('partial_sell', False):
                    logger.info(f"🔄 ČÁSTEČNÝ PRODEJ: {symbol}")
                    logger.info(f"   📊 Původní: {original_quantity:.6f}")
                    logger.info(f"   📊 Upraveno na 50%: {final_quantity:.6f}")
                    logger.info(f"   📝 Důvod: {validation['reason']}")
                else:
                    logger.info(f"🔄 UPRAVENÉ MNOŽSTVÍ: {symbol}")
                    logger.info(f"   📊 Původní: {original_quantity:.6f}")
                    logger.info(f"   📊 Upraveno: {final_quantity:.6f}")
                    logger.info(f"   📝 Důvod: {validation['reason']}")

            # Použití upraveného množství
            quantity = final_quantity
            
            # Zadání orderu
            order = self.exchange.create_market_order(symbol, side, quantity)
            
            if order and order.get('status') == 'closed':
                # Aktualizace pozice ve strategii
                fill_price = order.get('average', current_price)
                self.strategy.update_position(symbol, side, quantity, fill_price)
                
                # Výpočet SL/TP
                sl_tp = self.risk_manager.calculate_stop_loss_take_profit(fill_price, side)
                
                # Přidání pozice do risk manageru
                if side == 'buy':
                    self.risk_manager.add_position(
                        symbol, side, quantity, fill_price,
                        sl_tp['stop_loss'], sl_tp['take_profit']
                    )
                else:
                    self.risk_manager.remove_position(symbol)
                
                # Log obchodu s P&L pro uzavření pozice
                pnl = None
                if side == 'sell':
                    # Výpočet P&L pro uzavření pozice
                    position = self.strategy.get_position(symbol)
                    if position['quantity'] > 0:
                        pnl = (fill_price - position['entry_price']) * quantity

                logger.trade_log(side.upper(), symbol, fill_price, quantity, "Market order", pnl)

                # Discord notifikace
                signal_data = getattr(self, '_last_signal', {})
                confidence = signal_data.get('confidence', 50)
                reason = signal_data.get('reason', 'Market order')
                notifier.notify_trade(symbol, side.upper(), fill_price, quantity, reason, confidence)
                
                return order
            
        except Exception as e:
            logger.error(f"Chyba při zadávání orderu {side} {symbol}: {e}")
            return None
    
    def process_symbol(self, symbol: str):
        """Zpracování jednoho symbolu"""
        try:
            # NOVÝ: Aktualizace strategie podle tržních podmínek
            self.update_strategy_if_needed(symbol)

            # Získání dat
            ohlcv = self.fetch_ohlcv(symbol, Config.TIMEFRAME)
            if ohlcv is None or len(ohlcv) < Config.RSI_PERIOD:
                return
            
            # Aktualizace poslední ceny
            current_price = float(ohlcv['close'].iloc[-1])
            self.last_prices[symbol] = current_price
            
            # Aktualizace nerealizovaného P&L
            self.risk_manager.update_unrealized_pnl(symbol, current_price)
            
            # Kontrola exit podmínek (SL/TP)
            exit_check = self.risk_manager.check_exit_conditions(symbol, current_price)
            if exit_check['should_exit']:
                position = self.strategy.get_position(symbol)
                if position['quantity'] > 0:
                    order = self.place_market_order(symbol, 'sell', position['quantity'])
                    if order:
                        logger.info(f"Pozice uzavřena: {symbol} - {exit_check['reason']}")
                return
            
            # Analýza signálu
            signal = self.strategy.analyze_signal(symbol, ohlcv)
            self._last_signal = signal  # Uložení pro notifikace

            # Barevný výstup signálu
            from colors import print_strategy
            print_strategy(symbol, signal.get('rsi', 0), signal['action'], signal['reason'])

            if signal['action'] == 'BUY':
                balance = self.get_balance()
                if balance > 10:  # Minimální zůstatek
                    quantity = self.risk_manager.calculate_position_size(
                        balance, current_price, symbol
                    )
                    if quantity > 0:
                        order = self.place_market_order(symbol, 'buy', quantity)
                        if order:
                            # Barevný výstup pro otevřenou pozici
                            from colors import print_trade_open
                            print_trade_open(symbol, quantity, current_price, signal['reason'])

                            # Discord notifikace pro BUY
                            notifier.notify_trade(symbol, 'BUY', current_price, quantity,
                                                signal['reason'], signal['confidence'])

            elif signal['action'] == 'SELL':
                position = self.strategy.get_position(symbol)
                if position['quantity'] > 0:
                    order = self.place_market_order(symbol, 'sell', position['quantity'])
                    if order:
                        # Discord notifikace pro SELL
                        notifier.notify_trade(symbol, 'SELL', current_price, position['quantity'],
                                            signal['reason'], signal['confidence'])
                    
        except Exception as e:
            logger.error(f"Chyba při zpracování {symbol}: {e}")
    
    def run_cycle(self):
        """Jeden cyklus bota"""
        try:
            # Barevný začátek cyklu
            print_cycle_start()
            print_market_monitoring(len(Config.SYMBOLS))

            # Sledování aktivity v cyklu
            trades_made = 0

            # Zpracování všech symbolů
            for symbol in Config.SYMBOLS:
                trades_before = len(self.risk_manager.open_positions)
                self.process_symbol(symbol.strip())
                trades_after = len(self.risk_manager.open_positions)

                # Počítání provedených obchodů
                if trades_after != trades_before:
                    trades_made += 1

                time.sleep(1)  # Krátká pauza mezi symboly

            # Log portfolia
            portfolio = self.risk_manager.get_portfolio_summary()
            balance = self.get_balance()

            logger.balance_log(balance, portfolio['total_unrealized_pnl'])
            print_positions(portfolio['total_positions'])

            # Souhrn aktivity
            if trades_made > 0:
                activity_summary = f"Provedeno {trades_made} obchodů v tomto cyklu"
            elif portfolio['total_positions'] > 0:
                activity_summary = f"Sledování {portfolio['total_positions']} otevřených pozic"
            else:
                print_no_activity()
                activity_summary = "Žádná aktivita - čekání na RSI signály"

            return activity_summary

        except Exception as e:
            logger.error(f"Chyba v trading cyklu: {e}")
            return "Chyba v cyklu"
    
    def start(self):
        """Spuštění bota"""
        # Barevný banner
        print_banner("🤖 TRADING BOT SPUŠTĚN 🤖")

        # Validace konfigurace
        try:
            Config.validate()
        except ValueError as e:
            logger.error(f"Chyba konfigurace: {e}")
            return False

        # Inicializace burzy
        if not self.initialize_exchange():
            logger.error("Nepodařilo se připojit k burze")
            return False

        self.running = True
        print_banner("🚀 ENHANCED TRADING STRATEGY 🚀")
        print(success("🤖 Bot spuštěn - začíná AGRESIVNÍ trading"))
        print(highlight(f"📊 Symboly: {', '.join(Config.SYMBOLS)}"))
        print(highlight(f"⏱️  Interval: {Config.CHECK_INTERVAL} sekund (RYCHLÝ)"))
        print(highlight(f"📈 Timeframe: {Config.TIMEFRAME} (RYCHLÉ REAKCE)"))
        print(highlight(f"🎯 RSI: {Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT} (AGRESIVNÍ)"))
        print(highlight(f"📱 Discord: {'✅ Aktivní' if Config.DISCORD_WEBHOOK else '❌ Neaktivní'}"))

        # Discord notifikace o spuštění
        strategy_info = f"AGRESIVNÍ RSI {Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT} + MA20 + MACD + {Config.TIMEFRAME}"
        notifier.notify_startup(Config.SYMBOLS, strategy_info)

        try:
            while self.running:
                activity_summary = self.run_cycle()
                print_cycle_end(Config.CHECK_INTERVAL, activity_summary)
                time.sleep(Config.CHECK_INTERVAL)

        except KeyboardInterrupt:
            print(warning("\n⏹️  Bot zastaven uživatelem"))
        except Exception as e:
            logger.error(f"Neočekávaná chyba: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Zastavení bota"""
        self.running = False
        logger.info("=== Trading Bot zastaven ===")
        
        # Výpis finálního stavu
        portfolio = self.risk_manager.get_portfolio_summary()
        balance = self.get_balance()
        
        logger.info(f"Finální zůstatek: {balance} USDT")
        logger.info(f"Otevřené pozice: {portfolio['total_positions']}")
        logger.info(f"Celkový nerealizovaný P&L: {portfolio['total_unrealized_pnl']:.2f} USDT")
