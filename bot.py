import ccxt
import pandas as pd
import ta  # PŘIDÁNO: Potřebné pro technické indikátory
import time
import traceback  # PŘIDÁNO: Pro lepší error handling
from datetime import datetime
from typing import Dict, List, Optional, Any  # ROZŠÍŘENO: <PERSON><PERSON><PERSON> type hints

# Lokální importy s error handling
try:
    from config import Config, BINANCE_TESTNET, BYBIT_TESTNET
    from strategy import RSIStrategy
    from risk_management import RiskManager
    from logger import logger
    from colors import (
        print_cycle_start, print_cycle_end, print_positions,
        print_banner, success, error, warning, highlight,
        print_no_activity, print_market_monitoring, format_rsi, print_strategy
    )
    from simple_notifications import notifier
    from intelligent_strategy_selector import IntelligentStrategySelector
except ImportError as e:
    print(f"❌ Chyba importu: {e}")
    print("🔧 Zkontrolujte, zda existují všechny potřebné soubory")
    import sys
    sys.exit(1)

class TradingBot:
    """Hlavní třída trading bota"""
    
    def __init__(self):
        self.config = Config()
        self.exchange = None
        self.strategy = RSIStrategy(
            rsi_period=Config.RSI_PERIOD,
            oversold=Config.RSI_OVERSOLD,
            overbought=Config.RSI_OVERBOUGHT,
            ma_period=Config.MA_PERIOD
        )
        self.risk_manager = RiskManager(
            max_position_size=Config.MAX_POSITION_SIZE,
            stop_loss_pct=Config.STOP_LOSS_PERCENT,
            take_profit_pct=Config.TAKE_PROFIT_PERCENT
        )
        self.strategy_selector = IntelligentStrategySelector()  # NOVÝ: Inteligentní výběr strategie
        self.current_strategy_name = "Enhanced RSI Strategy"  # Aktuální strategie
        self.strategy_params = {}  # Parametry aktuální strategie
        self.strategy_update_counter = 0  # Počítadlo pro update strategie
        self.trading_mode = "balanced"  # NOVÝ: Trading mód (aggressive/conservative/balanced)
        self.startup_analysis_done = False  # NOVÝ: Flag pro startup analýzu
        self.running = False
        self.last_prices = {}

        # NOVÝ: Sledování změn pro inteligentní výpis
        self.last_balance = None
        self.last_positions_count = None
        self.last_signals = {}  # Poslední signály pro každý symbol
        self.last_rsi_values = {}  # Poslední RSI hodnoty
        self.unchanged_cycles = 0  # Počítadlo neměnných cyklů

        # NOVÝ: Sledování obchodů pro sumarizaci
        self.cycle_trades = []  # Obchody v aktuálním cyklu
        self.trade_history = {}  # Historie obchodů pro P&L výpočet
        
    def initialize_exchange(self):
        """Inicializace burzy"""
        try:
            if Config.EXCHANGE.lower() == 'binance':
                if Config.SANDBOX:
                    self.exchange = ccxt.binance(BINANCE_TESTNET)
                else:
                    self.exchange = ccxt.binance({
                        'apiKey': Config.API_KEY,
                        'secret': Config.API_SECRET,
                    })
            elif Config.EXCHANGE.lower() == 'bybit':
                if Config.SANDBOX:
                    self.exchange = ccxt.bybit(BYBIT_TESTNET)
                else:
                    self.exchange = ccxt.bybit({
                        'apiKey': Config.API_KEY,
                        'secret': Config.API_SECRET,
                    })
            else:
                raise ValueError(f"Nepodporovaná burza: {Config.EXCHANGE}")
            
            # Nastavení pro futures
            self.exchange.set_sandbox_mode(Config.SANDBOX)
            
            # Test připojení
            balance = self.exchange.fetch_balance()
            logger.info(f"Připojení k {Config.EXCHANGE} {'(testnet)' if Config.SANDBOX else ''} úspěšné")
            logger.info(f"Dostupný zůstatek: {balance.get('USDT', {}).get('free', 0)} USDT")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba při inicializaci burzy: {e}")
            return False
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """Získání OHLCV dat"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            return df
        except Exception as e:
            logger.error(f"Chyba při získávání dat pro {symbol}: {e}")
            return None
    
    def get_balance(self) -> float:
        """Získání dostupného zůstatku v USDT"""
        try:
            balance = self.exchange.fetch_balance()
            return float(balance.get('USDT', {}).get('free', 0))
        except Exception as e:
            logger.error(f"Chyba při získávání zůstatku: {e}")
            return 0.0

    def create_trade_summary(self) -> str:
        """Vytvoření sumarizace obchodů v cyklu s P&L informacemi"""
        if not self.cycle_trades:
            return "Žádné obchody v tomto cyklu"

        summary_parts = []
        total_pnl = 0

        for trade in self.cycle_trades:
            symbol = trade['symbol']
            side = trade['side']
            quantity = trade['quantity']
            price = trade['price']
            pnl = trade.get('pnl', 0)

            if side == 'BUY':
                # Nákup - zobrazit jako -částku (výdaj)
                cost = quantity * price
                summary_parts.append(f"{symbol} nákup -{cost:.0f}")
            elif side == 'SELL':
                # Prodej - zobrazit P&L
                if pnl is not None:
                    total_pnl += pnl
                    pnl_sign = "+" if pnl >= 0 else ""
                    summary_parts.append(f"{symbol} prodej {pnl_sign}{pnl:.0f}")
                else:
                    # Fallback - zobrazit tržbu
                    revenue = quantity * price
                    summary_parts.append(f"{symbol} prodej +{revenue:.0f}")

        # Vytvoření finálního souhrnu
        if summary_parts:
            trades_summary = ", ".join(summary_parts)
            if total_pnl != 0:
                pnl_sign = "+" if total_pnl >= 0 else ""
                trades_summary += f" | Celkem P&L: {pnl_sign}{total_pnl:.0f} USDT"
            return f"✅ Provedeno {len(self.cycle_trades)} obchodů: {trades_summary}"
        else:
            return f"✅ Provedeno {len(self.cycle_trades)} obchodů v tomto cyklu"

    def has_significant_change(self, symbol: str, current_rsi: float, action: str, balance: float, positions_count: int) -> bool:
        """Detekce významných změn pro inteligentní výpis"""
        has_change = False

        # Kontrola změny balance (rozdíl > 0.01 USDT)
        if self.last_balance is None or abs(balance - self.last_balance) > 0.01:
            has_change = True
            self.last_balance = balance

        # Kontrola změny počtu pozic
        if self.last_positions_count is None or positions_count != self.last_positions_count:
            has_change = True
            self.last_positions_count = positions_count

        # Kontrola změny signálu
        last_signal = self.last_signals.get(symbol, '')
        if action != last_signal:
            has_change = True
            self.last_signals[symbol] = action

        # Kontrola významné změny RSI (rozdíl > 2.0)
        last_rsi = self.last_rsi_values.get(symbol, 0)
        if abs(current_rsi - last_rsi) > 2.0:
            has_change = True
            self.last_rsi_values[symbol] = current_rsi

        # Reset počítadla při změně
        if has_change:
            self.unchanged_cycles = 0
        else:
            self.unchanged_cycles += 1

        return has_change

    def should_show_summary(self) -> bool:
        """Rozhodnutí zda zobrazit souhrn (každých 10 neměnných cyklů)"""
        return self.unchanged_cycles > 0 and self.unchanged_cycles % 10 == 0

    def update_strategy_if_needed(self, symbol: str):
        """Aktualizace strategie podle tržních podmínek"""
        try:
            # Aktualizace strategie každých 10 cyklů
            self.strategy_update_counter += 1
            if self.strategy_update_counter % 10 != 0:
                return

            logger.info(f"🧠 Analýza tržních podmínek pro {symbol}...")

            # Analýza trhu
            market_analysis = self.strategy_selector.analyze_market_conditions(self.exchange, symbol)

            if market_analysis:
                # Výběr optimální strategie
                optimal_strategy = self.strategy_selector.select_optimal_strategy(market_analysis)

                if optimal_strategy:
                    new_strategy_name = optimal_strategy['name']
                    confidence = optimal_strategy['confidence']

                    # Pokud se strategie změnila
                    if new_strategy_name != self.current_strategy_name:
                        logger.info(f"🔄 ZMĚNA STRATEGIE!")
                        logger.info(f"   📊 Stará: {self.current_strategy_name}")
                        logger.info(f"   📊 Nová: {new_strategy_name}")
                        logger.info(f"   🎯 Confidence: {confidence:.1f}%")

                        # Aktualizace strategie
                        self.current_strategy_name = new_strategy_name
                        self.strategy_params = self.strategy_selector.get_strategy_parameters(optimal_strategy)

                        # Aktualizace RSI parametrů
                        if 'rsi_oversold' in self.strategy_params:
                            self.strategy.oversold = self.strategy_params['rsi_oversold']
                            self.strategy.overbought = self.strategy_params['rsi_overbought']

                            logger.info(f"   📈 Nové RSI: {self.strategy.oversold}/{self.strategy.overbought}")

                        # Discord notifikace o změně strategie
                        trend = market_analysis['trend']
                        volatility = market_analysis['volatility']

                        strategy_change_msg = (
                            f"🧠 **ZMĚNA STRATEGIE**\n"
                            f"📊 **Nová strategie:** {new_strategy_name}\n"
                            f"🎯 **Confidence:** {confidence:.1f}%\n"
                            f"📈 **Trend:** {trend['strength']} (score: {trend['score']})\n"
                            f"📊 **Volatilita:** {volatility['level']} (score: {volatility['score']})\n"
                            f"🎯 **RSI:** {self.strategy_params.get('rsi_oversold', 45)}/{self.strategy_params.get('rsi_overbought', 55)}\n"
                            f"⏰ **Timeframe:** {self.strategy_params.get('timeframe', '5m')}"
                        )

                        notifier.send_message(strategy_change_msg)

                    # Výpis aktuální strategie
                    trend = market_analysis['trend']
                    volatility = market_analysis['volatility']

                    print(f"🧠 {highlight('AKTUÁLNÍ STRATEGIE')}: {success(new_strategy_name)}")
                    print(f"   📈 Trend: {trend['strength']} (score: {trend['score']})")
                    print(f"   📊 Volatilita: {volatility['level']} (score: {volatility['score']})")
                    print(f"   🎯 Confidence: {confidence:.1f}%")
                    print(f"   📊 RSI: {self.strategy_params.get('rsi_oversold', 45)}/{self.strategy_params.get('rsi_overbought', 55)}")

        except Exception as e:
            logger.error(f"Chyba při aktualizaci strategie: {e}")
    
    def place_market_order(self, symbol: str, side: str, quantity: float) -> Optional[Dict]:
        """Zadání market orderu s inteligentním sizing"""
        try:
            # Validace obchodu
            current_price = self.last_prices.get(symbol, 0)
            balance = self.get_balance()

            validation = self.risk_manager.validate_trade(
                symbol, side, quantity, current_price, balance
            )

            if not validation['valid']:
                logger.warning(f"Obchod zamítnut: {validation['reason']}")
                return None

            # Zpracování upraveného množství
            final_quantity = quantity
            if validation.get('adjusted', False):
                final_quantity = validation['adjusted_quantity']
                original_quantity = validation['original_quantity']

                if validation.get('partial_sell', False):
                    logger.info(f"🔄 ČÁSTEČNÝ PRODEJ: {symbol}")
                    logger.info(f"   📊 Původní: {original_quantity:.6f}")
                    logger.info(f"   📊 Upraveno na 50%: {final_quantity:.6f}")
                    logger.info(f"   📝 Důvod: {validation['reason']}")
                else:
                    logger.info(f"🔄 UPRAVENÉ MNOŽSTVÍ: {symbol}")
                    logger.info(f"   📊 Původní: {original_quantity:.6f}")
                    logger.info(f"   📊 Upraveno: {final_quantity:.6f}")
                    logger.info(f"   📝 Důvod: {validation['reason']}")

            # Použití upraveného množství
            quantity = final_quantity
            
            # Zadání orderu
            order = self.exchange.create_market_order(symbol, side, quantity)
            
            if order and order.get('status') == 'closed':
                # Aktualizace pozice ve strategii
                fill_price = order.get('average', current_price)
                self.strategy.update_position(symbol, side, quantity, fill_price)
                
                # Výpočet SL/TP
                sl_tp = self.risk_manager.calculate_stop_loss_take_profit(fill_price, side)
                
                # Přidání pozice do risk manageru
                if side == 'buy':
                    self.risk_manager.add_position(
                        symbol, side, quantity, fill_price,
                        sl_tp['stop_loss'], sl_tp['take_profit']
                    )
                else:
                    self.risk_manager.remove_position(symbol)
                
                # Log obchodu s P&L pro uzavření pozice
                pnl = None
                if side == 'sell':
                    # 🔧 OPRAVA: Výpočet P&L z risk manageru před uzavřením pozice
                    if symbol in self.risk_manager.open_positions:
                        rm_position = self.risk_manager.open_positions[symbol]
                        entry_price = rm_position['entry_price']
                        pnl = (fill_price - entry_price) * quantity
                        print(f"🔍 DEBUG P&L: Entry {entry_price:.4f}, Exit {fill_price:.4f}, Qty {quantity:.6f}, P&L {pnl:.2f}")
                    else:
                        # Fallback na strategy pozici
                        position = self.strategy.get_position(symbol)
                        if position['quantity'] > 0:
                            pnl = (fill_price - position['entry_price']) * quantity

                logger.trade_log(side.upper(), symbol, fill_price, quantity, "Market order", pnl)

                # 📊 NOVÉ: Sledování obchodu pro sumarizaci
                trade_info = {
                    'symbol': symbol,
                    'side': side.upper(),
                    'quantity': quantity,
                    'price': fill_price,
                    'reason': "Market order",
                    'pnl': pnl,
                    'timestamp': time.time()
                }
                self.cycle_trades.append(trade_info)

                # Uložení do historie pro budoucí P&L výpočty
                if symbol not in self.trade_history:
                    self.trade_history[symbol] = []
                self.trade_history[symbol].append(trade_info)

                # Discord notifikace
                signal_data = getattr(self, '_last_signal', {})
                confidence = signal_data.get('confidence', 50)
                reason = signal_data.get('reason', 'Market order')
                notifier.notify_trade(symbol, side.upper(), fill_price, quantity, reason, confidence)

                return order
            
        except Exception as e:
            logger.error(f"Chyba při zadávání orderu {side} {symbol}: {e}")
            return None
    
    def process_symbol(self, symbol: str):
        """Zpracování jednoho symbolu"""
        try:
            # NOVÝ: Aktualizace strategie podle tržních podmínek
            self.update_strategy_if_needed(symbol)

            # Získání dat
            ohlcv = self.fetch_ohlcv(symbol, Config.TIMEFRAME)
            if ohlcv is None or len(ohlcv) < Config.RSI_PERIOD:
                return

            # Adaptace podle trading módu
            if self.trading_mode == "conservative":
                # Konzervativní mód - vyšší RSI limity
                self.strategy.oversold = max(40, self.strategy.oversold)
                self.strategy.overbought = min(60, self.strategy.overbought)
            elif self.trading_mode == "aggressive":
                # Agresivní mód - nižší RSI limity
                self.strategy.oversold = min(50, self.strategy.oversold)
                self.strategy.overbought = max(50, self.strategy.overbought)
            
            # Aktualizace poslední ceny
            current_price = float(ohlcv['close'].iloc[-1])
            self.last_prices[symbol] = current_price
            
            # Aktualizace nerealizovaného P&L
            self.risk_manager.update_unrealized_pnl(symbol, current_price)
            
            # Kontrola exit podmínek (SL/TP)
            exit_check = self.risk_manager.check_exit_conditions(symbol, current_price)
            if exit_check['should_exit']:
                position = self.strategy.get_position(symbol)
                if position['quantity'] > 0:
                    order = self.place_market_order(symbol, 'sell', position['quantity'])
                    if order:
                        logger.info(f"Pozice uzavřena: {symbol} - {exit_check['reason']}")
                return
            
            # Analýza signálu
            signal = self.strategy.analyze_signal(symbol, ohlcv)
            self._last_signal = signal  # Uložení pro notifikace

            # 🎯 NOVÝ: Výpis POUZE při skutečných obchodech (BUY/SELL)
            balance = self.get_balance()
            positions_count = len(self.risk_manager.open_positions)
            current_rsi = signal.get('rsi', 0)

            # Kontrola významných změn
            has_change = self.has_significant_change(symbol, current_rsi, signal['action'], balance, positions_count)

            # VÝPIS POUZE PRO BUY/SELL nebo při významných změnách
            if signal['action'] in ['BUY', 'SELL']:
                # Vždy zobrazit skutečné obchodní signály
                from colors import print_strategy
                print_strategy(symbol, current_rsi, signal['action'], signal['reason'])
            elif has_change and signal['action'] == 'HOLD':
                # HOLD pouze při významné změně RSI (> 5 bodů)
                last_rsi = self.last_rsi_values.get(symbol, current_rsi)
                if abs(current_rsi - last_rsi) > 5.0:
                    from colors import print_strategy
                    print_strategy(symbol, current_rsi, signal['action'], signal['reason'])

            if signal['action'] == 'BUY':
                balance = self.get_balance()
                if balance > 10:  # Minimální zůstatek
                    quantity = self.risk_manager.calculate_position_size(
                        balance, current_price, symbol
                    )
                    if quantity > 0:
                        order = self.place_market_order(symbol, 'buy', quantity)
                        if order:
                            # Barevný výstup pro otevřenou pozici
                            from colors import print_trade_open
                            print_trade_open(symbol, quantity, current_price, signal['reason'])

                            # Discord notifikace pro BUY
                            notifier.notify_trade(symbol, 'BUY', current_price, quantity,
                                                signal['reason'], signal['confidence'])

            elif signal['action'] == 'SELL':
                position = self.strategy.get_position(symbol)
                if position['quantity'] > 0:
                    order = self.place_market_order(symbol, 'sell', position['quantity'])
                    if order:
                        # Discord notifikace pro SELL
                        notifier.notify_trade(symbol, 'SELL', current_price, position['quantity'],
                                            signal['reason'], signal['confidence'])
                    
        except Exception as e:
            logger.error(f"Chyba při zpracování {symbol}: {e}")
    
    def run_cycle(self):
        """Jeden cyklus bota"""
        try:
            # 🎯 NOVÝ: Reset obchodů pro nový cyklus
            self.cycle_trades = []

            # 🎯 NOVÝ: Tichý začátek cyklu (bez výpisu při každém cyklu)
            if self.unchanged_cycles == 0 or self.unchanged_cycles % 10 == 0:
                print_cycle_start()
                print_market_monitoring(len(Config.SYMBOLS))

            # Sledování aktivity v cyklu
            trades_made = 0

            # Zpracování všech symbolů
            for symbol in Config.SYMBOLS:
                trades_before = len(self.cycle_trades)

                # Uložení stavu před zpracováním
                old_unchanged_cycles = self.unchanged_cycles

                self.process_symbol(symbol.strip())
                trades_after = len(self.cycle_trades)

                # Počítání provedených obchodů
                if trades_after > trades_before:
                    trades_made += 1

                time.sleep(1)  # Krátká pauza mezi symboly

            # 🎯 NOVÝ: Inteligentní výpis portfolia pouze při změnách
            portfolio = self.risk_manager.get_portfolio_summary()
            balance = self.get_balance()

            # Kontrola změn balance nebo pozic
            balance_changed = self.last_balance is None or abs(balance - self.last_balance) > 0.01
            positions_changed = self.last_positions_count is None or portfolio['total_positions'] != self.last_positions_count

            # Výpis pouze při změnách nebo každých 10 cyklů
            if balance_changed or positions_changed or self.should_show_summary():
                logger.balance_log(balance, portfolio['total_unrealized_pnl'])
                print_positions(portfolio['total_positions'])

                # Aktualizace posledních hodnot
                self.last_balance = balance
                self.last_positions_count = portfolio['total_positions']

            # 🎯 VYLEPŠENÝ: Sumarizace obchodů s P&L informacemi
            if trades_made > 0:
                # Vytvoření detailní sumarizace obchodů
                activity_summary = self.create_trade_summary()
                print(f"📋 {success(activity_summary)}")
            elif portfolio['total_positions'] > 0:
                if positions_changed:
                    activity_summary = f"📊 Sledování {portfolio['total_positions']} otevřených pozic"
                    print(warning(activity_summary))
                elif self.should_show_summary():
                    activity_summary = f"📊 Monitoring {portfolio['total_positions']} pozic (cyklus {self.unchanged_cycles})"
                    print(warning(activity_summary))
                else:
                    activity_summary = f"Monitoring pozic... (tichý cyklus {self.unchanged_cycles})"
            else:
                # Tichý monitoring - výpis pouze každých 20 cyklů s ŽLUTOU barvou
                if self.unchanged_cycles % 20 == 0:
                    print_no_activity()
                    activity_summary = f"💤 Žádná aktivita - čekání na signály (cyklus {self.unchanged_cycles})"
                    # 🟡 ŽLUTÝ výstup pro idle stav (podle preferencí uživatele)
                    print(f"\033[93m{activity_summary}\033[0m")
                else:
                    activity_summary = f"Tichý monitoring... (cyklus {self.unchanged_cycles})"

            return activity_summary

        except Exception as e:
            logger.error(f"Chyba v trading cyklu: {e}")
            return "Chyba v cyklu"
    
    def startup_market_analysis(self):
        """🧠 STARTUP ANALÝZA TRHU - Analýza před spuštěním obchodování"""
        print_banner("🧠 STARTUP ANALÝZA TRŽNÍCH PODMÍNEK")

        try:
            market_conditions = {}
            overall_sentiment = {'bullish': 0, 'bearish': 0, 'neutral': 0}

            for symbol in Config.SYMBOLS:
                print(f"\n📊 Analyzuji {highlight(symbol)}...")

                # Získání dat pro analýzu
                ohlcv = self.fetch_ohlcv(symbol, Config.TIMEFRAME, limit=200)
                if ohlcv is None:
                    continue

                # Základní indikátory
                current_price = float(ohlcv['close'].iloc[-1])
                rsi = self.strategy.calculate_rsi(ohlcv['close'])
                ma20 = self.strategy.calculate_ma(ohlcv['close'])
                macd_data = self.strategy.calculate_macd(ohlcv['close'])

                # Trend analýza
                price_above_ma = current_price > ma20
                trend = "UPTREND" if price_above_ma else "DOWNTREND"
                momentum = "BULLISH" if macd_data['bullish'] else "BEARISH"

                # Inteligentní analýza strategie
                market_analysis = self.strategy_selector.analyze_market_conditions(self.exchange, symbol)
                if market_analysis:
                    optimal_strategy = self.strategy_selector.select_optimal_strategy(market_analysis)
                    strategy_name = optimal_strategy['name'] if optimal_strategy else "Default"
                    confidence = optimal_strategy['confidence'] if optimal_strategy else 50
                else:
                    strategy_name = "Default"
                    confidence = 50

                # Doporučení
                if rsi <= 35:
                    recommendation = "🚀 EXTRÉMNÍ NÁKUP"
                    sentiment = 'bullish'
                elif rsi <= 45 and price_above_ma:
                    recommendation = "✅ NÁKUP"
                    sentiment = 'bullish'
                elif rsi >= 55 and not price_above_ma:
                    recommendation = "❌ PRODEJ/ČEKÁNÍ"
                    sentiment = 'bearish'
                else:
                    recommendation = "⏸️ ČEKÁNÍ"
                    sentiment = 'neutral'

                overall_sentiment[sentiment] += 1

                # Výpis analýzy
                print(f"   💰 Cena: {current_price:.4f}")
                print(f"   📈 RSI: {format_rsi(rsi, 45, 55)}")
                print(f"   📊 Trend: {success(trend) if trend == 'UPTREND' else error(trend)}")
                print(f"   🎯 MACD: {success(momentum) if momentum == 'BULLISH' else error(momentum)}")
                print(f"   🧠 Strategie: {highlight(strategy_name)} ({confidence:.0f}%)")
                print(f"   💡 Doporučení: {recommendation}")

                market_conditions[symbol] = {
                    'price': current_price,
                    'rsi': rsi,
                    'trend': trend,
                    'momentum': momentum,
                    'strategy': strategy_name,
                    'confidence': confidence,
                    'recommendation': recommendation,
                    'sentiment': sentiment
                }

            # Celkový sentiment
            total_symbols = len(market_conditions)
            bullish_pct = (overall_sentiment['bullish'] / total_symbols) * 100
            bearish_pct = (overall_sentiment['bearish'] / total_symbols) * 100
            neutral_pct = (overall_sentiment['neutral'] / total_symbols) * 100

            print(f"\n📊 {highlight('CELKOVÝ TRŽNÍ SENTIMENT')}:")
            print(f"   🚀 Bullish: {success(f'{bullish_pct:.0f}%')} ({overall_sentiment['bullish']} symbolů)")
            print(f"   📉 Bearish: {error(f'{bearish_pct:.0f}%')} ({overall_sentiment['bearish']} symbolů)")
            print(f"   ⏸️ Neutral: {warning(f'{neutral_pct:.0f}%')} ({overall_sentiment['neutral']} symbolů)")

            # Doporučení pro spuštění
            if bullish_pct >= 60:
                overall_recommendation = "🚀 AGRESIVNÍ TRADING - Trh je bullish!"
                trading_mode = "aggressive"
            elif bearish_pct >= 60:
                overall_recommendation = "⚠️ OPATRNÝ TRADING - Trh je bearish!"
                trading_mode = "conservative"
            else:
                overall_recommendation = "📊 VYVÁŽENÝ TRADING - Smíšené podmínky"
                trading_mode = "balanced"

            print(f"\n🎯 {highlight('DOPORUČENÍ PRO TRADING')}:")
            print(f"   {overall_recommendation}")

            # Discord notifikace o startup analýze
            startup_analysis_msg = (
                f"🧠 **STARTUP ANALÝZA TRHU**\n"
                f"🚀 **Bullish:** {bullish_pct:.0f}% ({overall_sentiment['bullish']} symbolů)\n"
                f"📉 **Bearish:** {bearish_pct:.0f}% ({overall_sentiment['bearish']} symbolů)\n"
                f"⏸️ **Neutral:** {neutral_pct:.0f}% ({overall_sentiment['neutral']} symbolů)\n"
                f"🎯 **Režim:** {trading_mode.upper()}\n"
                f"💡 **Doporučení:** {overall_recommendation}"
            )

            notifier.send_message(startup_analysis_msg, "🧠 STARTUP ANALÝZA")

            return market_conditions, trading_mode

        except Exception as e:
            logger.error(f"Chyba při startup analýze: {e}")
            return {}, "balanced"

    def start(self):
        """Spuštění bota"""
        # Barevný banner
        print_banner("🤖 TRADING BOT SPUŠTĚN 🤖")

        # Validace konfigurace
        try:
            Config.validate()
        except ValueError as e:
            logger.error(f"Chyba konfigurace: {e}")
            return False

        # Inicializace burzy
        if not self.initialize_exchange():
            logger.error("Nepodařilo se připojit k burze")
            return False

        # 🧠 NOVÉ: STARTUP ANALÝZA TRHU
        print(f"\n{highlight('🧠 PROVÁDÍM STARTUP ANALÝZU TRHU...')}")
        market_conditions, trading_mode = self.startup_market_analysis()

        # Nastavení trading módu
        self.trading_mode = trading_mode
        print(f"\n🎯 Trading mód nastaven na: {highlight(trading_mode.upper())}")

        self.running = True
        print_banner("🚀 ENHANCED TRADING STRATEGY 🚀")
        print(success("🤖 Bot spuštěn - začíná AGRESIVNÍ trading"))
        print(highlight(f"📊 Symboly: {', '.join(Config.SYMBOLS)}"))
        print(highlight(f"⏱️  Interval: {Config.CHECK_INTERVAL} sekund (RYCHLÝ)"))
        print(highlight(f"📈 Timeframe: {Config.TIMEFRAME} (RYCHLÉ REAKCE)"))
        print(highlight(f"🎯 RSI: {Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT} (AGRESIVNÍ)"))
        print(highlight(f"📱 Discord: {'✅ Aktivní' if Config.DISCORD_WEBHOOK else '❌ Neaktivní'}"))

        # Discord notifikace o spuštění
        strategy_info = f"AGRESIVNÍ RSI {Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT} + MA20 + MACD + {Config.TIMEFRAME}"
        notifier.notify_startup(Config.SYMBOLS, strategy_info)

        try:
            while self.running:
                activity_summary = self.run_cycle()
                # Tichý režim - výpis pouze při aktivitě
                silent_mode = activity_summary.startswith("Tichý")
                print_cycle_end(Config.CHECK_INTERVAL, activity_summary, silent=silent_mode)
                time.sleep(Config.CHECK_INTERVAL)

        except KeyboardInterrupt:
            print(warning("\n⏹️  Bot zastaven uživatelem"))
        except Exception as e:
            logger.error(f"Neočekávaná chyba: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Zastavení bota"""
        self.running = False
        logger.info("=== Trading Bot zastaven ===")

        # 🔄 NOVÉ: Automatické uzavření všech pozic při zastavení (podle preferencí)
        try:
            portfolio = self.risk_manager.get_portfolio_summary()
            if portfolio['total_positions'] > 0:
                print(f"\n🔄 {highlight('AUTOMATICKÉ UZAVŘENÍ POZIC PŘI ZASTAVENÍ')}")
                print(f"📊 Nalezeno {portfolio['total_positions']} otevřených pozic")

                # Uzavření všech pozic
                for symbol in Config.SYMBOLS:
                    position = self.strategy.get_position(symbol)
                    if position['quantity'] > 0:
                        try:
                            current_price = self.get_current_price(symbol)
                            if current_price:
                                # Prodej celé pozice
                                self.execute_trade(symbol, 'sell', position['quantity'], current_price, "🔄 AUTOMATICKÉ UZAVŘENÍ")
                                print(f"   ✅ {symbol}: Uzavřeno {position['quantity']:.6f}")
                        except Exception as e:
                            logger.error(f"Chyba při uzavření {symbol}: {e}")

                print(f"✅ {success('Všechny pozice automaticky uzavřeny')}")
        except Exception as e:
            logger.error(f"Chyba při automatickém uzavření pozic: {e}")

        # Výpis finálního stavu
        portfolio = self.risk_manager.get_portfolio_summary()
        balance = self.get_balance()

        logger.info(f"Finální zůstatek: {balance} USDT")
        logger.info(f"Otevřené pozice: {portfolio['total_positions']}")
        logger.info(f"Celkový nerealizovaný P&L: {portfolio['total_unrealized_pnl']:.2f} USDT")
