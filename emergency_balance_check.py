#!/usr/bin/env python3
"""
🚨 EMERGENCY BALANCE CHECK
Okamžitá kontrola balance a analýza ztrát
"""

import ccxt
from config import Config, BINANCE_TESTNET, BYBIT_TESTNET
from colors import print_banner, success, error, warning, highlight

def emergency_balance_analysis():
    """Okamžitá analýza balance"""
    print_banner("🚨 EMERGENCY BALANCE ANALYSIS")
    
    try:
        # Inicializace burzy
        if Config.EXCHANGE.lower() == 'binance':
            if Config.SANDBOX:
                exchange = ccxt.binance(BINANCE_TESTNET)
            else:
                exchange = ccxt.binance({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        elif Config.EXCHANGE.lower() == 'bybit':
            if Config.SANDBOX:
                exchange = ccxt.bybit(BYBIT_TESTNET)
            else:
                exchange = ccxt.bybit({
                    'apiKey': Config.API_KEY,
                    'secret': Config.API_SECRET,
                })
        
        exchange.set_sandbox_mode(Config.SANDBOX)
        
        print(f"\n💰 {highlight('AKTUÁLNÍ STAV ÚČTU')}:")
        print("=" * 50)
        
        # Získání balance
        balance = exchange.fetch_balance()
        usdt_free = balance.get('USDT', {}).get('free', 0)
        usdt_used = balance.get('USDT', {}).get('used', 0)
        usdt_total = balance.get('USDT', {}).get('total', 0)
        
        print(f"💰 USDT Free: {error(f'{usdt_free:.2f}') if usdt_free < 13000 else success(f'{usdt_free:.2f}')}")
        print(f"🔒 USDT Used: {warning(f'{usdt_used:.2f}')}")
        print(f"📊 USDT Total: {error(f'{usdt_total:.2f}') if usdt_total < 13000 else success(f'{usdt_total:.2f}')}")
        
        # Analýza ztráty
        expected_balance = 15000.0  # Očekávaný začátek
        current_balance = usdt_total
        loss = expected_balance - current_balance
        loss_pct = (loss / expected_balance) * 100
        
        print(f"\n📉 {error('ANALÝZA ZTRÁTY')}:")
        print(f"   📊 Očekávaný balance: {success(f'{expected_balance:.2f} USDT')}")
        print(f"   💰 Aktuální balance: {error(f'{current_balance:.2f} USDT')}")
        print(f"   📉 Ztráta: {error(f'-{loss:.2f} USDT ({loss_pct:.1f}%)')}")
        
        # Získání otevřených pozic
        print(f"\n📊 {highlight('OTEVŘENÉ POZICE')}:")
        print("=" * 50)
        
        positions = exchange.fetch_positions()
        open_positions = [pos for pos in positions if pos['contracts'] > 0]
        
        total_unrealized_pnl = 0
        
        if open_positions:
            for position in open_positions:
                symbol = position['symbol']
                size = position['contracts']
                side = position['side']
                entry_price = position['entryPrice']
                mark_price = position['markPrice']
                unrealized_pnl = position['unrealizedPnl']
                pnl_pct = position['percentage']
                
                total_unrealized_pnl += unrealized_pnl
                
                pnl_color = success if unrealized_pnl > 0 else error if unrealized_pnl < 0 else warning
                
                print(f"\n📊 {highlight(symbol)}:")
                print(f"   📈 Size: {size}")
                print(f"   🎯 Side: {side}")
                print(f"   💰 Entry: {entry_price}")
                print(f"   📊 Mark: {mark_price}")
                print(f"   💸 P&L: {pnl_color(f'{unrealized_pnl:.2f} USDT ({pnl_pct:.2f}%)')}")
        else:
            print("✅ Žádné otevřené pozice")
        
        print(f"\n💰 Celkový unrealized P&L: {success(f'{total_unrealized_pnl:.2f} USDT') if total_unrealized_pnl > 0 else error(f'{total_unrealized_pnl:.2f} USDT')}")
        
        # Získání historie obchodů (posledních 50)
        print(f"\n📈 {highlight('HISTORIE OBCHODŮ (posledních 10)')}:")
        print("=" * 50)
        
        try:
            # Pro různé symboly
            symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
            all_trades = []
            
            for symbol in symbols:
                try:
                    trades = exchange.fetch_my_trades(symbol, limit=20)
                    all_trades.extend(trades)
                except:
                    continue
            
            # Seřazení podle času
            all_trades.sort(key=lambda x: x['timestamp'], reverse=True)
            
            total_realized_pnl = 0
            
            for trade in all_trades[:10]:  # Posledních 10 obchodů
                symbol = trade['symbol']
                side = trade['side']
                amount = trade['amount']
                price = trade['price']
                cost = trade['cost']
                fee = trade['fee']['cost'] if trade['fee'] else 0
                timestamp = trade['datetime']
                
                print(f"\n📊 {symbol}:")
                print(f"   🎯 {side.upper()}: {amount:.6f} @ {price:.4f}")
                print(f"   💰 Cost: {cost:.2f} USDT")
                print(f"   💸 Fee: {fee:.4f}")
                print(f"   ⏰ Time: {timestamp}")
                
                # Odhad P&L (zjednodušený)
                if side == 'sell':
                    # Najít odpovídající buy
                    for buy_trade in all_trades:
                        if (buy_trade['symbol'] == symbol and 
                            buy_trade['side'] == 'buy' and 
                            buy_trade['timestamp'] < trade['timestamp']):
                            pnl = (price - buy_trade['price']) * amount
                            total_realized_pnl += pnl
                            print(f"   📈 Estimated P&L: {success(f'+{pnl:.2f}') if pnl > 0 else error(f'{pnl:.2f}')} USDT")
                            break
            
            print(f"\n💰 Odhadovaný celkový realized P&L: {success(f'{total_realized_pnl:.2f} USDT') if total_realized_pnl > 0 else error(f'{total_realized_pnl:.2f} USDT')}")
            
        except Exception as e:
            print(f"⚠️ Nelze získat historii obchodů: {e}")
        
        # Celkový souhrn
        print(f"\n🎯 {highlight('CELKOVÝ SOUHRN')}:")
        print("=" * 50)
        
        total_pnl = total_unrealized_pnl + total_realized_pnl
        
        print(f"💰 Aktuální balance: {error(f'{current_balance:.2f} USDT')}")
        print(f"📊 Unrealized P&L: {success(f'{total_unrealized_pnl:.2f} USDT') if total_unrealized_pnl > 0 else error(f'{total_unrealized_pnl:.2f} USDT')}")
        print(f"📈 Realized P&L: {success(f'{total_realized_pnl:.2f} USDT') if total_realized_pnl > 0 else error(f'{total_realized_pnl:.2f} USDT')}")
        print(f"🎯 Total P&L: {success(f'{total_pnl:.2f} USDT') if total_pnl > 0 else error(f'{total_pnl:.2f} USDT')}")
        print(f"📉 Celková ztráta: {error(f'-{loss:.2f} USDT ({loss_pct:.1f}%)')}")
        
        # Doporučení
        print(f"\n🚨 {error('DOPORUČENÍ')}:")
        if loss > 1000:
            print("   ❌ KRITICKÁ ZTRÁTA! Okamžitě zastavte bota!")
            print("   🛑 Uzavřete všechny pozice")
            print("   🔍 Analyzujte co se pokazilo")
            print("   📊 Zkontrolujte trading strategii")
        elif loss > 500:
            print("   ⚠️ Významná ztráta! Buďte opatrní")
            print("   📊 Zkontrolujte risk management")
            print("   🎯 Možná snižte position size")
        else:
            print("   ✅ Ztráta v přijatelných mezích")
            print("   📊 Pokračujte s opatrností")
        
        return {
            'current_balance': current_balance,
            'loss': loss,
            'loss_pct': loss_pct,
            'unrealized_pnl': total_unrealized_pnl,
            'open_positions': len(open_positions)
        }
        
    except Exception as e:
        print(error(f"❌ Kritická chyba při analýze: {e}"))
        return None

def main():
    """Hlavní funkce"""
    print("🚨 EMERGENCY BALANCE CHECK")
    print("=" * 70)
    
    result = emergency_balance_analysis()
    
    if result:
        if result['loss'] > 1000:
            print(f"\n🚨 {error('KRITICKÁ SITUACE!')}")
            print(f"   💸 Ztráta: {result['loss']:.2f} USDT ({result['loss_pct']:.1f}%)")
            print(f"   🛑 OKAMŽITĚ ZASTAVTE BOTA!")
            print(f"   📞 python emergency_close_positions.py")
        else:
            print(f"\n📊 {warning('Situace pod kontrolou')}")
            print(f"   💸 Ztráta: {result['loss']:.2f} USDT ({result['loss_pct']:.1f}%)")
            print(f"   📊 Pokračujte s opatrností")

if __name__ == "__main__":
    main()
