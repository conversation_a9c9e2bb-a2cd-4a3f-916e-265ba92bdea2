#!/usr/bin/env python3
"""
OPTIMALIZOVANÝ AGRESIVNÍ TRADING BOT
Kompletně opravená a optimalizovaná verze
"""

import ccxt
import pandas as pd
import ta
import time
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

# Načtení konfigurace
load_dotenv()

class OptimizedTradingBot:
    """Optimalizovaný trading bot s agresivní strategií"""
    
    def __init__(self):
        # Konfigurace
        self.exchange_name = os.getenv('EXCHANGE', 'binance')
        self.api_key = os.getenv('API_KEY', '')
        self.api_secret = os.getenv('API_SECRET', '')
        self.sandbox = os.getenv('SANDBOX', 'True').lower() == 'true'
        
        # Trading parametry - AGRESIVNÍ
        self.symbols = [s.strip() for s in os.getenv('SYMBOLS', 'BTC/USDT,ETH/USDT,ADA/USDT').split(',')]
        self.timeframe = os.getenv('TIMEFRAME', '5m')
        self.check_interval = int(os.getenv('CHECK_INTERVAL', '30'))
        
        # RSI parametry - AGRESIVNÍ
        self.rsi_period = int(os.getenv('RSI_PERIOD', '14'))
        self.rsi_oversold = int(os.getenv('RSI_OVERSOLD', '40'))  # AGRESIVNÍ
        self.rsi_overbought = int(os.getenv('RSI_OVERBOUGHT', '60'))  # AGRESIVNÍ
        self.ma_period = int(os.getenv('MA_PERIOD', '20'))
        
        # Risk management
        self.position_size = float(os.getenv('MAX_POSITION_SIZE', '0.2'))
        self.stop_loss = float(os.getenv('STOP_LOSS_PERCENT', '3.0'))
        self.take_profit = float(os.getenv('TAKE_PROFIT_PERCENT', '5.0'))
        
        # Stav bota
        self.exchange = None
        self.running = False
        self.positions = {}
        self.balance = 0.0
        
    def initialize_exchange(self) -> bool:
        """Optimalizovaná inicializace burzy"""
        try:
            if self.exchange_name == 'binance':
                self.exchange = ccxt.binance({
                    'apiKey': self.api_key,
                    'secret': self.api_secret,
                    'sandbox': self.sandbox,
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'spot'  # Spot trading
                    }
                })
            elif self.exchange_name == 'bybit':
                self.exchange = ccxt.bybit({
                    'apiKey': self.api_key,
                    'secret': self.api_secret,
                    'sandbox': self.sandbox,
                    'enableRateLimit': True,
                })
            else:
                print(f"❌ Nepodporovaná burza: {self.exchange_name}")
                return False
            
            # Test připojení
            if self.api_key and self.api_secret:
                balance = self.exchange.fetch_balance()
                self.balance = balance.get('USDT', {}).get('free', 10000.0)
                print(f"✅ {self.exchange.name} připojen | Balance: {self.balance:,.2f} USDT")
            else:
                self.balance = 10000.0  # Demo balance
                print(f"✅ {self.exchange.name} připojen (demo mode)")
            
            return True
            
        except Exception as e:
            print(f"❌ Chyba připojení k burze: {e}")
            return False
    
    def get_market_data(self, symbol: str, limit: int = 50) -> Optional[pd.DataFrame]:
        """Optimalizované získání tržních dat"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            print(f"❌ Chyba získání dat pro {symbol}: {e}")
            return None
    
    def calculate_indicators(self, df: pd.DataFrame) -> Dict:
        """Optimalizovaný výpočet indikátorů"""
        try:
            # RSI
            rsi_indicator = ta.momentum.RSIIndicator(close=df['close'], window=self.rsi_period)
            current_rsi = rsi_indicator.rsi().iloc[-1]
            
            # Moving Average
            ma = df['close'].rolling(window=self.ma_period).mean()
            current_ma = ma.iloc[-1]
            current_price = df['close'].iloc[-1]
            
            # MACD
            macd_indicator = ta.trend.MACD(close=df['close'])
            macd_line = macd_indicator.macd().iloc[-1]
            macd_signal = macd_indicator.macd_signal().iloc[-1]
            
            return {
                'rsi': current_rsi,
                'price': current_price,
                'ma': current_ma,
                'price_above_ma': current_price > current_ma,
                'macd': macd_line,
                'macd_signal': macd_signal,
                'macd_bullish': macd_line > macd_signal
            }
        except Exception as e:
            print(f"❌ Chyba výpočtu indikátorů: {e}")
            return None
    
    def analyze_signal(self, symbol: str, indicators: Dict) -> Dict:
        """Optimalizovaná analýza signálu - AGRESIVNÍ strategie"""
        try:
            rsi = indicators['rsi']
            price = indicators['price']
            price_above_ma = indicators['price_above_ma']
            macd_bullish = indicators['macd_bullish']
            
            # AGRESIVNÍ BUY signály (RSI ≤ 40)
            if rsi <= self.rsi_oversold:
                if price_above_ma and macd_bullish:
                    return {
                        'action': 'BUY',
                        'signal': 'TRIPLE BUY',
                        'reason': f'RSI {rsi:.1f} ≤ {self.rsi_oversold} + UPTREND + MACD BULLISH',
                        'confidence': 90,
                        'emoji': '🟢'
                    }
                elif price_above_ma:
                    return {
                        'action': 'BUY',
                        'signal': 'STRONG BUY',
                        'reason': f'RSI {rsi:.1f} ≤ {self.rsi_oversold} + UPTREND',
                        'confidence': 75,
                        'emoji': '🟡'
                    }
                else:
                    return {
                        'action': 'BUY',
                        'signal': 'WEAK BUY',
                        'reason': f'RSI {rsi:.1f} ≤ {self.rsi_oversold} (oversold)',
                        'confidence': 60,
                        'emoji': '🔵'
                    }
            
            # AGRESIVNÍ SELL signály (RSI ≥ 60)
            elif rsi >= self.rsi_overbought:
                if not price_above_ma and not macd_bullish:
                    return {
                        'action': 'SELL',
                        'signal': 'TRIPLE SELL',
                        'reason': f'RSI {rsi:.1f} ≥ {self.rsi_overbought} + DOWNTREND + MACD BEARISH',
                        'confidence': 90,
                        'emoji': '🔴'
                    }
                else:
                    return {
                        'action': 'SELL',
                        'signal': 'SELL',
                        'reason': f'RSI {rsi:.1f} ≥ {self.rsi_overbought} (overbought)',
                        'confidence': 70,
                        'emoji': '🟠'
                    }
            
            # HOLD
            else:
                return {
                    'action': 'HOLD',
                    'signal': 'HOLD',
                    'reason': f'RSI {rsi:.1f} v neutrální zóně ({self.rsi_oversold}-{self.rsi_overbought})',
                    'confidence': 0,
                    'emoji': '⏸️'
                }
                
        except Exception as e:
            print(f"❌ Chyba analýzy signálu: {e}")
            return {
                'action': 'HOLD',
                'signal': 'ERROR',
                'reason': f'Chyba: {str(e)}',
                'confidence': 0,
                'emoji': '❌'
            }
    
    def print_signal(self, symbol: str, indicators: Dict, signal: Dict):
        """Optimalizovaný barevný výstup signálu"""
        rsi = indicators['rsi']
        price = indicators['price']
        emoji = signal['emoji']
        signal_type = signal['signal']
        reason = signal['reason']
        confidence = signal['confidence']
        
        print(f"{emoji} {signal_type}: {symbol} | RSI: {rsi:.1f} | ${price:,.2f}")
        print(f"   📝 {reason}")
        if confidence > 0:
            print(f"   🎯 Confidence: {confidence}%")
    
    def simulate_trade(self, symbol: str, signal: Dict, price: float):
        """Simulace obchodu"""
        action = signal['action']
        
        if action == 'BUY' and symbol not in self.positions:
            quantity = (self.balance * self.position_size) / price
            self.positions[symbol] = {
                'side': 'long',
                'quantity': quantity,
                'entry_price': price,
                'stop_loss': price * (1 - self.stop_loss / 100),
                'take_profit': price * (1 + self.take_profit / 100)
            }
            print(f"🚀 OTEVŘENO {symbol} | {quantity:.6f} @ ${price:,.2f}")
            print(f"   📊 SL: ${self.positions[symbol]['stop_loss']:,.2f} | TP: ${self.positions[symbol]['take_profit']:,.2f}")
            
        elif action == 'SELL' and symbol in self.positions:
            position = self.positions[symbol]
            pnl = (price - position['entry_price']) * position['quantity']
            print(f"💰 UZAVŘENO {symbol} | P&L: {pnl:+.2f} USDT")
            del self.positions[symbol]
    
    def run_cycle(self) -> int:
        """Optimalizovaný trading cyklus"""
        current_time = datetime.now().strftime('%H:%M:%S')
        print(f"\n{'='*70}")
        print(f"🤖 AGRESIVNÍ TRADING CYKLUS - {current_time}")
        print(f"🎯 RSI {self.rsi_oversold}/{self.rsi_overbought} | {self.timeframe} | {len(self.symbols)} symbolů")
        print(f"{'='*70}")
        
        signals_found = 0
        
        for symbol in self.symbols:
            print(f"\n👁️ Analýza {symbol}...")
            
            # Získání dat
            df = self.get_market_data(symbol)
            if df is None:
                continue
            
            # Výpočet indikátorů
            indicators = self.calculate_indicators(df)
            if indicators is None:
                continue
            
            # Analýza signálu
            signal = self.analyze_signal(symbol, indicators)
            
            # Výstup signálu
            self.print_signal(symbol, indicators, signal)
            
            # Simulace obchodu
            if signal['action'] in ['BUY', 'SELL']:
                self.simulate_trade(symbol, signal, indicators['price'])
                signals_found += 1
            
            time.sleep(1)  # Pauza mezi symboly
        
        # Souhrn cyklu
        print(f"\n📋 Souhrn cyklu:")
        print(f"   🎯 Nalezeno signálů: {signals_found}")
        print(f"   💎 Balance: {self.balance:,.2f} USDT")
        print(f"   🔥 Otevřené pozice: {len(self.positions)}")
        
        if signals_found > 0:
            print(f"   ✅ AKTIVNÍ TRADING!")
        else:
            print(f"   ⏸️ Žádná aktivita - čekání na signály")
        
        return signals_found
    
    def start(self):
        """Spuštění optimalizovaného bota"""
        print("🚀" + "="*70 + "🚀")
        print("🤖 OPTIMALIZOVANÝ AGRESIVNÍ TRADING BOT 🤖")
        print(f"🎯 RSI {self.rsi_oversold}/{self.rsi_overbought} | {self.timeframe} | {self.check_interval}s interval")
        print(f"📊 Symboly: {', '.join(self.symbols)}")
        print(f"🏢 Exchange: {self.exchange_name} ({'testnet' if self.sandbox else 'live'})")
        print("🚀" + "="*70 + "🚀")
        
        if not self.initialize_exchange():
            return False
        
        self.running = True
        cycle = 0
        
        try:
            while self.running:
                cycle += 1
                print(f"\n🔄 CYKLUS #{cycle}")
                
                signals = self.run_cycle()
                
                print(f"\n⏰ Čekání {self.check_interval} sekund do dalšího cyklu...")
                time.sleep(self.check_interval)
                
        except KeyboardInterrupt:
            print(f"\n⏹️ Bot zastaven uživatelem")
        except Exception as e:
            print(f"\n❌ Neočekávaná chyba: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.stop()
    
    def stop(self):
        """Zastavení bota"""
        self.running = False
        print(f"\n✅ Bot zastaven")
        
        if self.positions:
            print(f"📊 Otevřené pozice při zastavení:")
            for symbol, pos in self.positions.items():
                pnl = (pos.get('current_price', pos['entry_price']) - pos['entry_price']) * pos['quantity']
                print(f"   {symbol}: {pos['quantity']:.6f} @ ${pos['entry_price']:,.2f} | P&L: {pnl:+.2f}")

def main():
    """Hlavní funkce"""
    bot = OptimizedTradingBot()
    bot.start()

if __name__ == "__main__":
    main()
