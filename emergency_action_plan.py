#!/usr/bin/env python3
"""
🚨 EMERGENCY ACTION PLAN
Okamžitý plán pro řešení kritick<PERSON> ztr<PERSON>ty balance
"""

from colors import print_banner, success, error, warning, highlight

def show_critical_situation():
    """Zobrazení kritické situace"""
    print_banner("🚨 KRITICKÁ SITUACE - ZTRÁTA 2700 USDT")
    
    print(f"\n📊 {error('ANALÝZA ZTRÁTY')}:")
    print("=" * 60)
    print(f"💰 Původní balance: ~15000 USDT")
    print(f"💸 Aktuální balance: 12281.64 USDT")
    print(f"📉 Ztráta: {error('-2718.36 USDT (18.1%)')}")
    print(f"🚨 Status: {error('KRITICKÝ')}")

def show_immediate_actions():
    """Okamžité kroky"""
    print(f"\n🛑 {error('OKAMŽITÉ KROKY - PROVEĎTE IHNED')}:")
    print("=" * 60)
    
    actions = [
        {
            'step': 1,
            'action': 'ZASTAVTE BOTA',
            'command': 'Ctrl + C v terminálu kde běží bot',
            'priority': 'KRITICKÉ'
        },
        {
            'step': 2,
            'action': 'UZAVŘETE VŠECHNY POZICE',
            'command': 'python emergency_close_positions.py',
            'priority': 'KRITICKÉ'
        },
        {
            'step': 3,
            'action': 'ZKONTROLUJTE ÚČET',
            'command': 'python emergency_balance_check.py',
            'priority': 'VYSOKÉ'
        },
        {
            'step': 4,
            'action': 'ANALYZUJTE OBCHODY',
            'command': 'Zkontrolujte historii obchodů',
            'priority': 'STŘEDNÍ'
        }
    ]
    
    for action in actions:
        priority_color = error if action['priority'] == 'KRITICKÉ' else warning if action['priority'] == 'VYSOKÉ' else highlight
        print(f"\n{action['step']}. {priority_color(action['action'])} ({action['priority']})")
        print(f"   💻 {action['command']}")

def show_possible_causes():
    """Možné příčiny ztráty"""
    print(f"\n🔍 {highlight('MOŽNÉ PŘÍČINY ZTRÁTY')}:")
    print("=" * 60)
    
    causes = {
        "❌ AGRESIVNÍ PARAMETRY": [
            "RSI 45/55 - příliš agresivní",
            "Position size 25% - příliš velké pozice",
            "Timeframe 3m - příliš rychlý",
            "Check interval 20s - příliš častý"
        ],
        "❌ ŠPATNÝ MARKET TIMING": [
            "Spuštění v bear market",
            "Vysoká volatilita",
            "Klesající trend všech symbolů",
            "Špatné tržní podmínky"
        ],
        "❌ EMERGENCY SELL SELHÁVÁ": [
            "Pozice se neuzavírají v downtrend",
            "Stop loss nefunguje správně",
            "P&L výpočet je chybný",
            "Risk management selhává"
        ],
        "❌ TECHNICKÉ PROBLÉMY": [
            "Chyby v API komunikaci",
            "Špatné order execution",
            "Slippage při obchodech",
            "Fees vyšší než očekávané"
        ]
    }
    
    for cause, details in causes.items():
        print(f"\n{cause}:")
        for detail in details:
            print(f"   • {detail}")

def show_implemented_fixes():
    """Implementované opravy"""
    print(f"\n✅ {success('IMPLEMENTOVANÉ OPRAVY')}:")
    print("=" * 60)
    
    fixes = {
        "🛡️ KONZERVATIVNÍ NASTAVENÍ": [
            "RSI 25/75 (místo 45/55)",
            "Position size 10% (místo 25%)",
            "Timeframe 15m (místo 3m)",
            "Check interval 60s (místo 20s)"
        ],
        "🚨 EMERGENCY PROTECTION": [
            "Emergency sell v downtrend",
            "Ochrana kapitálu prioritou",
            "Stop loss při MACD bearish",
            "Uzavření pozic pod MA20"
        ],
        "📊 LEPŠÍ MONITORING": [
            "Debug P&L výpočtu",
            "Balance check script",
            "Emergency close script",
            "Detailní analýza obchodů"
        ]
    }
    
    for fix, details in fixes.items():
        print(f"\n{fix}:")
        for detail in details:
            print(f"   ✅ {detail}")

def show_recovery_plan():
    """Plán obnovy"""
    print(f"\n🔄 {highlight('PLÁN OBNOVY')}:")
    print("=" * 60)
    
    recovery_steps = [
        {
            'phase': 'FÁZE 1: ZASTAVENÍ ZTRÁT',
            'duration': 'Okamžitě',
            'actions': [
                'Zastavit bota',
                'Uzavřít všechny pozice',
                'Analyzovat škody',
                'Identifikovat příčiny'
            ]
        },
        {
            'phase': 'FÁZE 2: ANALÝZA A OPRAVA',
            'duration': '1-2 dny',
            'actions': [
                'Detailní analýza obchodů',
                'Oprava parametrů',
                'Testování na demo účtu',
                'Backtesting nových nastavení'
            ]
        },
        {
            'phase': 'FÁZE 3: OPATRNÝ RESTART',
            'duration': '1 týden',
            'actions': [
                'Konzervativní parametry',
                'Malé position sizes',
                'Pečlivé sledování',
                'Postupné zvyšování aktivit'
            ]
        },
        {
            'phase': 'FÁZE 4: NORMALIZACE',
            'duration': '2-4 týdny',
            'actions': [
                'Postupné zvyšování parametrů',
                'Monitoring výkonnosti',
                'Optimalizace strategie',
                'Návrat k normálnímu tradingu'
            ]
        }
    ]
    
    for step in recovery_steps:
        print(f"\n📅 {highlight(step['phase'])} ({step['duration']}):")
        for action in step['actions']:
            print(f"   • {action}")

def show_prevention_measures():
    """Preventivní opatření"""
    print(f"\n🛡️ {highlight('PREVENTIVNÍ OPATŘENÍ DO BUDOUCNA')}:")
    print("=" * 60)
    
    measures = [
        "📊 Denní limit ztrát (max 2% balance)",
        "🛑 Automatické zastavení při ztrátě 5%",
        "📈 Backtesting před každou změnou",
        "⏰ Pravidelné kontroly výkonnosti",
        "📱 Okamžité notifikace při problémech",
        "🎯 Konzervativnější position sizing",
        "📊 Lepší market timing analýza",
        "🔍 Detailnější monitoring P&L"
    ]
    
    for measure in measures:
        print(f"   ✅ {measure}")

def main():
    """Hlavní funkce"""
    print("🚨 EMERGENCY ACTION PLAN - ZTRÁTA 2700 USDT")
    print("=" * 70)
    
    show_critical_situation()
    show_immediate_actions()
    show_possible_causes()
    show_implemented_fixes()
    show_recovery_plan()
    show_prevention_measures()
    
    print(f"\n🎯 {error('NEJDŮLEŽITĚJŠÍ KROKY TERAZ')}:")
    print(f"   1. {error('ZASTAVTE BOTA')} (Ctrl+C)")
    print(f"   2. {error('UZAVŘETE POZICE')} (python emergency_close_positions.py)")
    print(f"   3. {warning('ZKONTROLUJTE ÚČET')} (python emergency_balance_check.py)")
    print(f"   4. {highlight('ANALYZUJTE PŘÍČINY')}")
    print(f"   5. {success('IMPLEMENTUJTE OPRAVY')}")
    
    print(f"\n💡 {highlight('PAMATUJTE')}:")
    print(f"   • Ztráta 18% je vážná, ale ne fatální")
    print(f"   • Důležité je zastavit další ztráty")
    print(f"   • Konzervativní přístup je nyní klíčový")
    print(f"   • Postupná obnova je lepší než rychlé řešení")

if __name__ == "__main__":
    main()
