#!/usr/bin/env python3
"""
Trading Bot - Hlavní spouště<PERSON><PERSON> soubor
Autor: Trading Bot System
Datum: 2024

Použití:
    python main.py --mode live          # Spuštění live tradingu
    python main.py --mode backtest      # Spuštění backtestingu
    python main.py --mode test          # Test připojení
"""

import argparse
import sys
import os
from datetime import datetime

# Přidání aktuálního adres<PERSON>e do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bot import TradingBot
from backtesting import Backtester
from config import Config
from logger import logger
from utils import print_backtest_summary, plot_backtest_results, save_backtest_results

def test_connection():
    """Test připojení k burze"""
    logger.info("=== Test připojení k burze ===")
    
    try:
        bot = TradingBot()
        if bot.initialize_exchange():
            balance = bot.get_balance()
            logger.info(f"✅ Připojení úspěšné!")
            logger.info(f"✅ Dostupný zůstatek: {balance} USDT")
            logger.info(f"✅ Burza: {Config.EXCHANGE} {'(testnet)' if Config.SANDBOX else '(live)'}")
            logger.info(f"✅ Symboly: {', '.join(Config.SYMBOLS)}")
            return True
        else:
            logger.error("❌ Připojení se nezdařilo")
            return False
    except Exception as e:
        logger.error(f"❌ Chyba při testu připojení: {e}")
        return False

def run_backtest():
    """Spuštění backtestingu"""
    logger.info("=== Spouštění Backtestingu ===")
    
    try:
        backtester = Backtester(initial_balance=Config.INITIAL_BALANCE)
        
        # Spuštění backtestingu pro každý symbol
        all_results = {}
        
        for symbol in Config.SYMBOLS:
            symbol = symbol.strip()
            logger.info(f"Backtesting pro {symbol}...")
            
            results = backtester.run_backtest(
                symbol=symbol,
                start_date=Config.BACKTEST_START,
                end_date=Config.BACKTEST_END
            )
            
            if 'error' not in results:
                all_results[symbol] = results
                
                # Výpis výsledků
                print(f"\n{'='*20} {symbol} {'='*20}")
                print_backtest_summary(results)
                
                # Uložení výsledků
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"backtest_{symbol.replace('/', '_')}_{timestamp}.json"
                save_backtest_results(results, filename)
                
                # Vytvoření grafu
                try:
                    plot_backtest_results(results, symbol, save_plot=True)
                except Exception as e:
                    logger.warning(f"Nepodařilo se vytvořit graf pro {symbol}: {e}")
            else:
                logger.error(f"Backtest pro {symbol} selhal: {results['error']}")
        
        # Souhrn všech symbolů
        if all_results:
            print(f"\n{'='*50}")
            print("           CELKOVÝ SOUHRN")
            print(f"{'='*50}")
            
            total_trades = sum(r['total_trades'] for r in all_results.values())
            total_pnl = sum(r['total_pnl'] for r in all_results.values())
            avg_return = sum(r['total_return'] for r in all_results.values()) / len(all_results)
            
            print(f"Testované symboly: {len(all_results)}")
            print(f"Celkový počet obchodů: {total_trades}")
            print(f"Celkový P&L: {total_pnl:.2f} USDT")
            print(f"Průměrný výnos: {avg_return:.2f}%")
            print(f"{'='*50}")
        
        return True
        
    except Exception as e:
        logger.error(f"Chyba při backtestingu: {e}")
        return False

def run_live_trading():
    """Spuštění live tradingu"""
    logger.info("=== Spouštění Live Tradingu ===")
    
    # Kontrola konfigurace
    if not Config.SANDBOX:
        response = input("⚠️  POZOR: Spouštíte live trading s reálnými penězi! Pokračovat? (ano/ne): ")
        if response.lower() not in ['ano', 'yes', 'y']:
            logger.info("Live trading zrušen uživatelem")
            return False
    
    try:
        bot = TradingBot()
        bot.start()
        return True
    except Exception as e:
        logger.error(f"Chyba při live tradingu: {e}")
        return False

def main():
    """Hlavní funkce"""
    parser = argparse.ArgumentParser(description='Trading Bot System')
    parser.add_argument(
        '--mode', 
        choices=['live', 'backtest', 'test'], 
        default='test',
        help='Režim spuštění (default: test)'
    )
    
    args = parser.parse_args()
    
    # Výpis konfigurace
    print(f"\n{'='*50}")
    print("         TRADING BOT SYSTEM")
    print(f"{'='*50}")
    print(f"Režim: {args.mode.upper()}")
    print(f"Burza: {Config.EXCHANGE} {'(testnet)' if Config.SANDBOX else '(live)'}")
    print(f"Symboly: {', '.join(Config.SYMBOLS)}")
    print(f"Timeframe: {Config.TIMEFRAME}")
    print(f"RSI parametry: {Config.RSI_PERIOD}/{Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT}")
    print(f"Risk management: {Config.MAX_POSITION_SIZE*100}% / SL:{Config.STOP_LOSS_PERCENT}% / TP:{Config.TAKE_PROFIT_PERCENT}%")
    print(f"{'='*50}\n")
    
    # Spuštění podle režimu
    success = False
    
    if args.mode == 'test':
        success = test_connection()
    elif args.mode == 'backtest':
        success = run_backtest()
    elif args.mode == 'live':
        success = run_live_trading()
    
    if success:
        logger.info("✅ Program dokončen úspěšně")
        sys.exit(0)
    else:
        logger.error("❌ Program dokončen s chybou")
        sys.exit(1)

if __name__ == "__main__":
    main()
