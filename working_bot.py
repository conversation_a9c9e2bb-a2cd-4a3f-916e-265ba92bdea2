#!/usr/bin/env python3
"""
WORKING AGRESIVNÍ TRADING BOT
Garantovaně funguj<PERSON><PERSON><PERSON> verze s reálnými daty
"""

import ccxt
import pandas as pd
import ta
import time
from datetime import datetime

def print_banner():
    print("🚀" + "="*60 + "🚀")
    print("🤖 AGRESIVNÍ TRADING BOT - LIVE VERZE 🤖")
    print("🎯 RSI 40/60 | 5m timeframe | 30s interval 🎯")
    print("🚀" + "="*60 + "🚀")

def get_rsi_signal(symbol, exchange):
    """Získání RSI signálu pro symbol"""
    try:
        # Získání dat
        ohlcv = exchange.fetch_ohlcv(symbol, '5m', limit=30)
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        # RSI výpočet
        rsi_indicator = ta.momentum.RSIIndicator(close=df['close'], window=14)
        df['rsi'] = rsi_indicator.rsi()
        current_rsi = df['rsi'].iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # MA20 pro trend
        ma20 = df['close'].rolling(window=20).mean().iloc[-1]
        price_above_ma = current_price > ma20
        trend = "UPTREND" if price_above_ma else "DOWNTREND"
        
        # AGRESIVNÍ signály
        if current_rsi <= 40:
            if price_above_ma:
                signal = "TRIPLE BUY"
                emoji = "🟢"
                confidence = 85
            else:
                signal = "STRONG BUY"
                emoji = "🟡"
                confidence = 65
        elif current_rsi >= 60:
            if not price_above_ma:
                signal = "TRIPLE SELL"
                emoji = "🔴"
                confidence = 85
            else:
                signal = "SELL"
                emoji = "🟠"
                confidence = 65
        else:
            signal = "HOLD"
            emoji = "⏸️"
            confidence = 0
        
        return {
            'symbol': symbol,
            'price': current_price,
            'rsi': current_rsi,
            'ma20': ma20,
            'trend': trend,
            'signal': signal,
            'emoji': emoji,
            'confidence': confidence
        }
        
    except Exception as e:
        print(f"❌ Chyba pro {symbol}: {e}")
        return None

def print_signal(data):
    """Barevný výstup signálu"""
    if not data:
        return
    
    symbol = data['symbol']
    price = data['price']
    rsi = data['rsi']
    signal = data['signal']
    emoji = data['emoji']
    confidence = data['confidence']
    trend = data['trend']
    
    print(f"{emoji} {signal}: {symbol} | RSI: {rsi:.1f} | ${price:,.2f} | {trend}")
    if confidence > 0:
        print(f"   🎯 Confidence: {confidence}%")

def main():
    """Hlavní funkce"""
    print_banner()
    
    # Inicializace burzy
    try:
        exchange = ccxt.binance({
            'apiKey': '732164a6ab71af1710328a9b637e8f0c5d2889d036731aa20c842667f7dc18b2',
            'secret': '91266127c55d71a9e59ae7777d4bba4cd05eb2a9209114cabb5b8ab8bab6994e',
            'sandbox': True,
            'enableRateLimit': True,
        })
        print("✅ Binance testnet připojen")
        
        # Test připojení
        balance = exchange.fetch_balance()
        usdt_balance = balance.get('USDT', {}).get('free', 0)
        print(f"💰 USDT Balance: {usdt_balance:,.2f}")
        
    except Exception as e:
        print(f"❌ Chyba připojení: {e}")
        return
    
    # Trading symboly
    symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
    
    print(f"\n📊 Sledované symboly: {', '.join(symbols)}")
    print(f"⏰ Interval: 30 sekund")
    print(f"📈 Timeframe: 5 minut")
    print(f"🎯 RSI: 40/60 (AGRESIVNÍ)")
    
    cycle = 0
    
    try:
        while True:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            print(f"\n{'='*70}")
            print(f"🤖 TRADING CYKLUS #{cycle} - {current_time}")
            print(f"{'='*70}")
            
            signals_found = 0
            
            for symbol in symbols:
                print(f"\n👁️ Analýza {symbol}...")
                
                signal_data = get_rsi_signal(symbol, exchange)
                if signal_data:
                    print_signal(signal_data)
                    
                    if signal_data['signal'] in ['TRIPLE BUY', 'STRONG BUY', 'TRIPLE SELL', 'SELL']:
                        signals_found += 1
                        
                        # Simulace obchodu
                        if 'BUY' in signal_data['signal']:
                            print(f"🚀 SIMULACE: Nákup {symbol} za ${signal_data['price']:,.2f}")
                        else:
                            print(f"💰 SIMULACE: Prodej {symbol} za ${signal_data['price']:,.2f}")
                
                time.sleep(2)  # Pauza mezi symboly
            
            # Souhrn cyklu
            print(f"\n📋 Souhrn cyklu:")
            print(f"   🎯 Nalezeno signálů: {signals_found}")
            print(f"   💎 Balance: {usdt_balance:,.2f} USDT")
            
            if signals_found > 0:
                print(f"   🔥 AKTIVNÍ TRADING!")
            else:
                print(f"   ⏸️ Žádná aktivita - čekání na signály")
            
            print(f"\n⏰ Čekání 30 sekund do dalšího cyklu...")
            time.sleep(30)
            
    except KeyboardInterrupt:
        print(f"\n⏹️ Bot zastaven uživatelem")
    except Exception as e:
        print(f"\n❌ Neočekávaná chyba: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n✅ Bot ukončen")

if __name__ == "__main__":
    main()
