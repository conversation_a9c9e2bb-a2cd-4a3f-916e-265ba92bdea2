#!/usr/bin/env python3
"""
Test reálného API připojení a získání aktuálních dat
"""

import ccxt
import os
from dotenv import load_dotenv
from colors import print_banner, success, error, warning, highlight
import time

def test_real_connection():
    """Test připojení k reálné burze"""
    load_dotenv()
    
    print_banner("🔌 TEST REÁLNÉHO API PŘIPOJENÍ 🔌")
    
    # Načtení konfigurace
    api_key = os.getenv('API_KEY', '')
    api_secret = os.getenv('API_SECRET', '')
    exchange_name = os.getenv('EXCHANGE', 'binance')
    
    if not api_key or not api_secret:
        print(error("❌ API klíče nejsou nastavené!"))
        print(warning("Nastavte API_KEY a API_SECRET v .env souboru"))
        return False
    
    print(f"🔑 API Key: {api_key[:8]}...")
    print(f"🏢 Exchange: {exchange_name}")
    
    try:
        # Vytvoření exchange objektu
        if exchange_name == 'binance':
            exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': True,  # Testnet
                'enableRateLimit': True,
            })
        elif exchange_name == 'bybit':
            exchange = ccxt.bybit({
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': True,  # Testnet
                'enableRateLimit': True,
            })
        else:
            print(error(f"❌ Nepodporovaná burza: {exchange_name}"))
            return False
        
        print(f"✅ Exchange objekt vytvořen: {exchange.name}")
        
        # Test připojení
        print(f"\n{highlight('📊 TESTOVÁNÍ PŘIPOJENÍ...')}")
        
        # 1. Test balance
        try:
            balance = exchange.fetch_balance()
            print(success(f"✅ Balance načten: {balance.get('USDT', {}).get('free', 0)} USDT"))
        except Exception as e:
            print(error(f"❌ Chyba při načítání balance: {e}"))
        
        # 2. Test tržních dat
        try:
            ticker = exchange.fetch_ticker('BTC/USDT')
            price = ticker['last']
            print(success(f"✅ BTC/USDT cena: ${price:,.2f}"))
            
            # Test OHLCV dat
            ohlcv = exchange.fetch_ohlcv('BTC/USDT', '5m', limit=10)
            print(success(f"✅ OHLCV data načtena: {len(ohlcv)} svíček"))
            
            # Zobrazení posledních cen
            print(f"\n{highlight('📈 POSLEDNÍCH 5 SVÍČEK (5m):')}")
            for i, candle in enumerate(ohlcv[-5:]):
                timestamp, open_price, high, low, close, volume = candle
                dt = exchange.iso8601(timestamp)
                print(f"  {i+1}. {dt}: O:{open_price} H:{high} L:{low} C:{close}")
            
        except Exception as e:
            print(error(f"❌ Chyba při načítání tržních dat: {e}"))
            return False
        
        # 3. Test RSI výpočtu
        try:
            from ta import add_all_ta_features
            import pandas as pd
            
            # Převod na DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Výpočet RSI
            from ta.momentum import RSIIndicator
            rsi_indicator = RSIIndicator(close=df['close'], window=14)
            df['rsi'] = rsi_indicator.rsi()
            
            current_rsi = df['rsi'].iloc[-1]
            print(success(f"✅ Aktuální RSI: {current_rsi:.2f}"))
            
            # Analýza signálu
            if current_rsi <= 40:
                signal = "🟢 STRONG BUY"
                color = success
            elif current_rsi >= 60:
                signal = "🔴 STRONG SELL"  
                color = error
            else:
                signal = "🟡 HOLD"
                color = warning
            
            print(color(f"📊 Trading signál: {signal}"))
            
        except Exception as e:
            print(warning(f"⚠️ Chyba při výpočtu RSI: {e}"))
        
        print(f"\n{success('🎉 REÁLNÉ API PŘIPOJENÍ FUNGUJE! 🎉')}")
        return True
        
    except Exception as e:
        print(error(f"❌ Chyba při připojení: {e}"))
        return False

def show_next_steps():
    """Zobrazení dalších kroků"""
    print(f"\n{print_banner('🚀 DALŠÍ KROKY 🚀')}")
    
    print(highlight("1. 🎯 SPUŠTĚNÍ LIVE BOTA:"))
    print(success("python main.py --mode live"))
    
    print(f"\n{highlight('2. 📊 NOVÁ AGRESIVNÍ STRATEGIE:')}")
    print("   • RSI 40/60 (místo 35/65)")
    print("   • 5m timeframe (místo 1h)")
    print("   • 30s kontroly (místo 60s)")
    print("   • Více trading signálů!")
    
    print(f"\n{highlight('3. 📱 SLEDOVÁNÍ:')}")
    print("   • Sledujte logy v terminálu")
    print("   • Discord notifikace (pokud nastavené)")
    print("   • Graf equity curve")
    
    print(f"\n{warning('⚠️ POZOR:')}")
    print("Používáte TESTNET - žádné reálné peníze!")

if __name__ == "__main__":
    if test_real_connection():
        show_next_steps()
    else:
        print(f"\n{error('❌ OPRAVTE API NASTAVENÍ A ZKUSTE ZNOVU!')}")
