#!/usr/bin/env python3
"""
Barevný výstup pro trading bot
Podporuje Windows, Linux a Mac
"""

import sys
import os

class Colors:
    """Barevné kódy pro terminál"""
    
    # Základní barvy
    BLACK = '\033[30m'
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    GRAY = '\033[90m'
    
    # Styly
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    ITALIC = '\033[3m'
    
    # Reset
    RESET = '\033[0m'
    
    # Pozadí
    BG_RED = '\033[101m'
    BG_GREEN = '\033[102m'
    BG_YELLOW = '\033[103m'
    BG_BLUE = '\033[104m'
    
    # Emoji pro Windows kompatibilitu
    ROCKET = '🚀'
    MONEY = '💰'
    CHART_UP = '📈'
    CHART_DOWN = '📉'
    WARNING = '⚠️'
    CHECK = '✅'
    CROSS = '❌'
    ROBOT = '🤖'
    FIRE = '🔥'
    DIAMOND = '💎'

def init_colors():
    """Inicializace barev pro Windows"""
    if sys.platform == "win32":
        try:
            # Povolení ANSI escape sekvencí na Windows 10+
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
        except:
            # Fallback pro starší Windows
            pass

def colorize(text, color=Colors.WHITE, style="", bg=""):
    """Obarvení textu"""
    if not sys.stdout.isatty():
        # Pokud výstup není terminál (např. soubor), nepoužívat barvy
        return text
    
    return f"{style}{bg}{color}{text}{Colors.RESET}"

def success(text):
    """Zelený text pro úspěch"""
    return colorize(text, Colors.GREEN, Colors.BOLD)

def error(text):
    """Červený text pro chybu"""
    return colorize(text, Colors.RED, Colors.BOLD)

def warning(text):
    """Žlutý text pro varování"""
    return colorize(text, Colors.YELLOW, Colors.BOLD)

def info(text):
    """Modrý text pro informace"""
    return colorize(text, Colors.BLUE)

def highlight(text):
    """Zvýrazněný text"""
    return colorize(text, Colors.CYAN, Colors.BOLD)

def profit(text):
    """Zelený text pro zisk"""
    return colorize(text, Colors.GREEN, Colors.BOLD)

def loss(text):
    """Červený text pro ztrátu"""
    return colorize(text, Colors.RED, Colors.BOLD)

def neutral(text):
    """Šedý text pro neutrální"""
    return colorize(text, Colors.GRAY)

def buy_signal(text):
    """Zelené pozadí pro nákup"""
    return colorize(text, Colors.WHITE, Colors.BOLD, Colors.BG_GREEN)

def sell_signal(text):
    """Červené pozadí pro prodej"""
    return colorize(text, Colors.WHITE, Colors.BOLD, Colors.BG_RED)

def hold_signal(text):
    """Žluté pozadí s černým textem pro hold/čekání - ČITELNÉ"""
    return colorize(text, Colors.BLACK, Colors.BOLD, Colors.BG_YELLOW)

def format_price(price, change=None):
    """Formátování ceny s barvou podle změny"""
    price_str = f"{price:.2f}"
    
    if change is None:
        return highlight(price_str)
    elif change > 0:
        return profit(f"{price_str} (+{change:.2f})")
    elif change < 0:
        return loss(f"{price_str} ({change:.2f})")
    else:
        return neutral(price_str)

def format_percentage(pct):
    """Formátování procent s barvou"""
    if pct > 0:
        return profit(f"+{pct:.2f}%")
    elif pct < 0:
        return loss(f"{pct:.2f}%")
    else:
        return neutral(f"{pct:.2f}%")

def format_pnl(pnl):
    """Formátování P&L s barvou"""
    if pnl > 0:
        return profit(f"+{pnl:.2f} USDT")
    elif pnl < 0:
        return loss(f"{pnl:.2f} USDT")
    else:
        return neutral(f"{pnl:.2f} USDT")

def format_rsi(rsi, oversold=40, overbought=60):
    """Formátování RSI s barvou podle úrovně - AGRESIVNÍ"""
    if rsi <= oversold:
        return buy_signal(f"RSI: {rsi:.2f}")
    elif rsi >= overbought:
        return sell_signal(f"RSI: {rsi:.2f}")
    elif rsi <= oversold + 5:  # Blízko oversold (40-45)
        return colorize(f"RSI: {rsi:.2f}", Colors.GREEN)
    elif rsi >= overbought - 5:  # Blízko overbought (55-60)
        return colorize(f"RSI: {rsi:.2f}", Colors.RED)
    else:
        return warning(f"RSI: {rsi:.2f}")  # Neutrální zóna - žlutě

def trade_header():
    """Hlavička pro obchody"""
    return colorize("=" * 60, Colors.CYAN, Colors.BOLD)

def print_banner(title):
    """Barevný banner"""
    banner = f"""
{colorize('=' * 60, Colors.CYAN, Colors.BOLD)}
{colorize(f'  {Colors.ROBOT} {title.upper()} {Colors.ROBOT}', Colors.YELLOW, Colors.BOLD)}
{colorize('=' * 60, Colors.CYAN, Colors.BOLD)}
"""
    print(banner)

def print_trade_open(symbol, quantity, price, reason):
    """Barevný výpis otevření pozice"""
    print(f"{Colors.ROCKET} {buy_signal(' OTEVŘENO ')} {success(symbol)} | "
          f"{highlight(f'{quantity:.6f}')} @ {profit(f'{price:.2f}')} | "
          f"{info(reason)}")

def print_trade_close(symbol, quantity, price, pnl, reason):
    """Barevný výpis uzavření pozice"""
    pnl_color = profit if pnl >= 0 else loss
    print(f"{Colors.MONEY} {sell_signal(' UZAVŘENO ')} {success(symbol)} | "
          f"{highlight(f'{quantity:.6f}')} @ {warning(f'{price:.2f}')} | "
          f"P&L: {pnl_color(f'{pnl:+.2f} USDT')} | {info(reason)}")

def print_balance(balance, pnl=None):
    """Barevný výpis balance"""
    balance_str = f"{Colors.DIAMOND} Balance: {highlight(f'{balance:.2f} USDT')}"
    if pnl is not None:
        pnl_str = format_pnl(pnl)
        balance_str += f" | P&L: {pnl_str}"
    print(balance_str)

def print_strategy(symbol, rsi, action, reason):
    """Barevný výpis strategie"""
    rsi_colored = format_rsi(rsi)

    if action == 'BUY':
        action_colored = buy_signal(f' {action} ')
        emoji = Colors.CHART_UP
    elif action == 'SELL':
        action_colored = sell_signal(f' {action} ')
        emoji = Colors.CHART_DOWN
    elif action == 'HOLD':
        action_colored = hold_signal(f' {action} ')
        emoji = "⏸️"
    else:
        action_colored = neutral(f' {action} ')
        emoji = "📊"

    print(f"{emoji} {highlight(symbol)} | {rsi_colored} | "
          f"{action_colored} | {info(reason)}")

def print_cycle_start():
    """Začátek trading cyklu"""
    print(f"\n{colorize('=== TRADING CYKLUS ===', Colors.MAGENTA, Colors.BOLD)}")

def print_cycle_end(interval, activity_summary="", silent=False):
    """Konec trading cyklu"""
    if not silent:
        if activity_summary and not activity_summary.startswith("Tichý"):
            print(f"📋 {highlight(activity_summary)}")
        if not activity_summary.startswith("Tichý"):
            print(f"⏰ {neutral(f'Čekání {interval} sekund do dalšího cyklu...')}\n")

def print_positions(count):
    """Výpis počtu pozic"""
    if count > 0:
        print(f"{Colors.FIRE} Otevřené pozice: {highlight(str(count))}")
    else:
        print(f"💤 {warning('Otevřené pozice: 0')}")

def print_no_activity():
    """Výpis pro žádnou aktivitu"""
    print(f"💤 {warning('Žádná trading aktivita - čekání na signály...')}")

def print_market_monitoring(symbols_count):
    """Výpis pro sledování trhu"""
    print(f"👁️ {info(f'Sledování {symbols_count} symbolů...')}")

def print_error(message):
    """Barevný výpis chyby"""
    print(f"{Colors.CROSS} {error(message)}")

def print_success(message):
    """Barevný výpis úspěchu"""
    print(f"{Colors.CHECK} {success(message)}")

def print_warning(message):
    """Barevný výpis varování"""
    print(f"{Colors.WARNING} {warning(message)}")

# Inicializace při importu
init_colors()
