#!/usr/bin/env python3
"""
Adaptivní risk management
"""

import pandas as pd
from typing import Dict, List
from datetime import datetime, timedelta

class AdaptiveRiskManager:
    """Adaptivní risk management podle volatility a výkonu"""
    
    def __init__(self):
        self.trade_history = []
        self.base_position_size = 0.2  # 20%
        self.base_stop_loss = 3.0      # 3%
        self.base_take_profit = 5.0    # 5%
    
    def calculate_volatility(self, prices: pd.Series, period: int = 20) -> float:
        """Výpočet volatility (ATR-based)"""
        if len(prices) < period:
            return 0.02  # Default 2%
        
        returns = prices.pct_change().dropna()
        volatility = returns.rolling(window=period).std().iloc[-1]
        return volatility if not pd.isna(volatility) else 0.02
    
    def get_recent_performance(self, days: int = 30) -> Dict:
        """Analýza nedávného výkonu"""
        if not self.trade_history:
            return {'win_rate': 0.5, 'avg_return': 0, 'total_trades': 0}
        
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_trades = [t for t in self.trade_history if t['date'] > cutoff_date]
        
        if not recent_trades:
            return {'win_rate': 0.5, 'avg_return': 0, 'total_trades': 0}
        
        wins = len([t for t in recent_trades if t['pnl'] > 0])
        total = len(recent_trades)
        win_rate = wins / total if total > 0 else 0.5
        
        avg_return = sum(t['pnl_percent'] for t in recent_trades) / total
        
        return {
            'win_rate': win_rate,
            'avg_return': avg_return,
            'total_trades': total
        }
    
    def calculate_adaptive_position_size(self, symbol: str, confidence: int, 
                                       volatility: float, balance: float) -> float:
        """Adaptivní velikost pozice"""
        performance = self.get_recent_performance()
        
        # Základní velikost
        position_size = self.base_position_size
        
        # Úprava podle confidence (50-100%)
        confidence_multiplier = confidence / 100
        position_size *= confidence_multiplier
        
        # Úprava podle volatility
        if volatility > 0.05:  # Vysoká volatilita
            position_size *= 0.7  # Snížit pozici
        elif volatility < 0.02:  # Nízká volatilita
            position_size *= 1.2  # Zvýšit pozici
        
        # Úprava podle výkonu
        if performance['win_rate'] > 0.7 and performance['total_trades'] >= 5:
            position_size *= 1.3  # Zvýšit při dobrém výkonu
        elif performance['win_rate'] < 0.3 and performance['total_trades'] >= 5:
            position_size *= 0.6  # Snížit při špatném výkonu
        
        # Limity
        position_size = max(0.05, min(0.4, position_size))  # 5-40%
        
        return position_size
    
    def calculate_adaptive_stops(self, volatility: float, confidence: int) -> Dict:
        """Adaptivní stop loss a take profit"""
        
        # Základní hodnoty
        stop_loss = self.base_stop_loss
        take_profit = self.base_take_profit
        
        # Úprava podle volatility
        volatility_multiplier = max(0.5, min(2.0, volatility / 0.03))
        stop_loss *= volatility_multiplier
        take_profit *= volatility_multiplier
        
        # Úprava podle confidence
        if confidence > 80:
            take_profit *= 1.5  # Větší TP při vysoké jistotě
        elif confidence < 60:
            stop_loss *= 0.8    # Menší SL při nízké jistotě
            take_profit *= 0.8
        
        # Limity
        stop_loss = max(1.0, min(8.0, stop_loss))      # 1-8%
        take_profit = max(2.0, min(15.0, take_profit))  # 2-15%
        
        return {
            'stop_loss_percent': stop_loss,
            'take_profit_percent': take_profit,
            'risk_reward_ratio': take_profit / stop_loss
        }
    
    def add_trade(self, symbol: str, side: str, entry_price: float, 
                  exit_price: float, quantity: float, pnl: float):
        """Přidání obchodu do historie"""
        pnl_percent = (pnl / (entry_price * quantity)) * 100
        
        trade = {
            'date': datetime.now(),
            'symbol': symbol,
            'side': side,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'quantity': quantity,
            'pnl': pnl,
            'pnl_percent': pnl_percent
        }
        
        self.trade_history.append(trade)
        
        # Udržet jen posledních 100 obchodů
        if len(self.trade_history) > 100:
            self.trade_history = self.trade_history[-100:]
    
    def get_risk_assessment(self, symbol: str, confidence: int, 
                           prices: pd.Series, balance: float) -> Dict:
        """Kompletní risk assessment"""
        volatility = self.calculate_volatility(prices)
        position_size = self.calculate_adaptive_position_size(symbol, confidence, volatility, balance)
        stops = self.calculate_adaptive_stops(volatility, confidence)
        performance = self.get_recent_performance()
        
        return {
            'position_size_percent': position_size * 100,
            'position_value': balance * position_size,
            'stop_loss_percent': stops['stop_loss_percent'],
            'take_profit_percent': stops['take_profit_percent'],
            'risk_reward_ratio': stops['risk_reward_ratio'],
            'volatility': volatility * 100,
            'recent_win_rate': performance['win_rate'] * 100,
            'recent_trades': performance['total_trades'],
            'risk_level': 'LOW' if volatility < 0.02 else 'MEDIUM' if volatility < 0.05 else 'HIGH'
        }
