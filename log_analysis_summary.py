#!/usr/bin/env python3
"""
📊 RYCHLÁ ANALÝZA LOGŮ
Souhrn toho, co jsem našel v logách
"""

from colors import print_banner, success, error, highlight, warning

def analyze_logs():
    """Analýza logů"""
    print_banner("📊 ANALÝZA LOGŮ - ZJIŠTĚNÍ")
    
    print(f"\n🔍 {highlight('CO JSEM NAŠEL V LOGÁCH')}:")
    print("=" * 60)
    
    findings = {
        "📅 ČASOVÉ OBDOBÍ": "2025-06-16 (včera) - pouze demo režim",
        "🤖 REŽIM": "Demo mode - simulované obchodování",
        "💰 BALANCE": "Demo balance 10000 USDT",
        "📊 SYMBOLY": "BTC/USDT, ETH/USDT, ADA/USDT",
        "🎯 RSI PARAMETRY": "<PERSON><PERSON>le staré (30 oversold detekováno)",
        "📈 OBCHODY": "Našel jsem BUY signály při RSI 27.92!"
    }
    
    for key, value in findings.items():
        print(f"   {key}: {value}")
    
    print(f"\n✅ {success('POZITIVNÍ ZJIŠTĚNÍ')}:")
    positive_findings = [
        "Bot správně detekuje RSI ≤ 30 jako oversold",
        "Našel jsem BUY signály při RSI 27.92 (řádek 347)",
        "Bot otevřel 3 pozice současně při oversold",
        "Position sizing funguje (2000, 1600, 1280 USDT)",
        "Stop Loss a Take Profit se nastavují",
        "Portfolio hodnota se sleduje správně"
    ]
    
    for finding in positive_findings:
        print(f"   ✅ {finding}")
    
    print(f"\n⚠️ {warning('ZJIŠTĚNÉ PROBLÉMY')}:")
    problems = [
        "Logy jsou pouze z demo režimu (ne live trading)",
        "Žádné logy s novými RSI 30/60 parametry",
        "Žádné PROFIT TAKING při RSI 55",
        "Žádné STOP LOSS při RSI 25",
        "Žádné EMERGENCY SELL signály",
        "Žádné DEBUG P&L výpisy",
        "Žádná STARTUP ANALÝZA"
    ]
    
    for problem in problems:
        print(f"   ⚠️ {problem}")
    
    print(f"\n🔍 {highlight('KLÍČOVÉ NÁLEZY Z LOGŮ')}:")
    print("=" * 60)
    
    key_findings = [
        {
            'řádek': 347,
            'obsah': 'STRATEGY: BTC/USDT | RSI: 27.92 | Action: BUY | RSI 27.92 <= 30 (oversold)',
            'význam': 'Bot správně nakupuje při RSI ≤ 30'
        },
        {
            'řádek': 348,
            'obsah': 'OTEVŘENO: BUY 0.047627 BTC/USDT @ 41993.01 | Hodnota: 2000.00 USDT',
            'význam': 'Position sizing 2000 USDT (20% z 10000)'
        },
        {
            'řádek': 381,
            'obsah': 'STRATEGY: BTC/USDT | RSI: 72.53 | Action: HOLD | RSI 72.53 v neutrální zóně',
            'význam': 'Bot NEUZAVÍRÁ při RSI 72.53 (měl by při 60!)'
        },
        {
            'řádek': 532,
            'obsah': 'Portfolio hodnota: 16311.90 USDT',
            'význam': 'Demo bot vydělal +6311 USDT (+63%!)'
        }
    ]
    
    for finding in key_findings:
        print(f"\n📍 Řádek {finding['řádek']}:")
        print(f"   📝 {finding['obsah']}")
        print(f"   💡 {finding['význam']}")

def show_rsi_analysis():
    """Analýza RSI hodnot z logů"""
    print(f"\n📈 {highlight('ANALÝZA RSI HODNOT Z LOGŮ')}:")
    print("=" * 60)
    
    rsi_examples = [
        {'rsi': 27.92, 'action': 'BUY', 'status': '✅ SPRÁVNĚ - pod 30'},
        {'rsi': 30.08, 'action': 'HOLD', 'status': '✅ SPRÁVNĚ - těsně nad 30'},
        {'rsi': 35.08, 'action': 'HOLD', 'status': '⚠️ MĚLO BY BÝT BUY (nové parametry)'},
        {'rsi': 58.51, 'action': 'HOLD', 'status': '❌ MĚLO BY BÝT SELL při RSI 55+'},
        {'rsi': 72.53, 'action': 'HOLD', 'status': '❌ MĚLO BY BÝT SELL při RSI 60+'},
        {'rsi': 76.71, 'action': 'HOLD', 'status': '❌ URČITĚ SELL - velmi overbought'}
    ]
    
    print(f"📊 Příklady RSI hodnot z logů:")
    for example in rsi_examples:
        print(f"   RSI {example['rsi']:5.2f} | {example['action']:4} | {example['status']}")

def show_conclusion():
    """Závěr analýzy"""
    print(f"\n🎯 {highlight('ZÁVĚR ANALÝZY LOGŮ')}:")
    print("=" * 60)
    
    print(f"\n✅ {success('CO FUNGUJE')}:")
    working = [
        "Základní RSI logika (nákup při ≤30)",
        "Position sizing a risk management",
        "Demo režim a backtesting",
        "Sledování portfolio hodnoty",
        "Stop Loss a Take Profit nastavení"
    ]
    
    for item in working:
        print(f"   ✅ {item}")
    
    print(f"\n❌ {error('CO NEFUNGUJE (PODLE LOGŮ)')}:")
    not_working = [
        "Nové RSI 30/60 parametry nejsou v logách",
        "PROFIT TAKING při RSI 55 - nenalezeno",
        "STOP LOSS při RSI 25 - nenalezeno", 
        "EMERGENCY SELL - nenalezeno",
        "DEBUG P&L výpisy - nenalezeno",
        "STARTUP ANALÝZA - nenalezeno",
        "Tichý výstup - nenalezeno"
    ]
    
    for item in not_working:
        print(f"   ❌ {item}")
    
    print(f"\n💡 {highlight('DOPORUČENÍ')}:")
    recommendations = [
        "Spusťte bota s novými parametry v live režimu",
        "Zkontrolujte, zda se nové funkce skutečně používají",
        "Sledujte logy v reálném čase během běhu",
        "Otestujte PROFIT TAKING a STOP LOSS logiku",
        "Ověřte, že STARTUP ANALÝZA funguje"
    ]
    
    for rec in recommendations:
        print(f"   💡 {rec}")

def show_demo_performance():
    """Výkonnost demo bota"""
    print(f"\n📈 {highlight('VÝKONNOST DEMO BOTA')}:")
    print("=" * 60)
    
    performance = {
        'Počáteční balance': '10000.00 USDT',
        'Konečná portfolio hodnota': '16311.90 USDT',
        'Zisk': '+6311.90 USDT',
        'Výnos': '+63.12%',
        'Otevřené pozice': '3 (BTC, ETH, ADA)',
        'Strategie': 'Buy and Hold při oversold'
    }
    
    for key, value in performance.items():
        if 'Zisk' in key or 'Výnos' in key:
            print(f"   {key}: {success(value)}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n💡 Demo bot vydělal 63% za 90 dní!")
    print(f"   To je důkaz, že základní logika funguje!")

def main():
    """Hlavní funkce"""
    print("📊 ANALÝZA LOGŮ - KONTROLA IMPLEMENTACE RSI 30/60")
    print("=" * 70)
    
    analyze_logs()
    show_rsi_analysis()
    show_demo_performance()
    show_conclusion()
    
    print(f"\n🚨 {error('HLAVNÍ PROBLÉM')}:")
    print(f"   Logy obsahují pouze demo režim ze včerejška")
    print(f"   Nové RSI 30/60 parametry nejsou v logách vidět")
    print(f"   Potřebujeme spustit live bota s novými parametry!")
    
    print(f"\n🚀 {highlight('DALŠÍ KROKY')}:")
    print(f"   1. Spusťte: python main.py --mode live")
    print(f"   2. Sledujte nové logy v reálném čase")
    print(f"   3. Ověřte nové funkce (PROFIT TAKING, STOP LOSS)")
    print(f"   4. Zkontrolujte STARTUP ANALÝZU")

if __name__ == "__main__":
    main()
