#!/usr/bin/env python3
"""
Vylepšená strategie s více indikátory
"""

import pandas as pd
import ta
from typing import Dict
from logger import logger

class EnhancedStrategy:
    """Vylepšená trading strategie s více indikátory"""
    
    def __init__(self, rsi_period=14, ma_period=20, bb_period=20):
        self.rsi_period = rsi_period
        self.ma_period = ma_period
        self.bb_period = bb_period
        self.positions = {}
    
    def calculate_indicators(self, data: pd.DataFrame) -> Dict:
        """Výpočet všech indikátorů"""
        close = data['close']
        high = data['high']
        low = data['low']
        volume = data['volume'] if 'volume' in data.columns else None
        
        indicators = {}
        
        # 1. RSI
        indicators['rsi'] = ta.momentum.RSIIndicator(close, window=self.rsi_period).rsi().iloc[-1]
        
        # 2. Moving Average (trend)
        indicators['ma'] = close.rolling(window=self.ma_period).mean().iloc[-1]
        indicators['price_above_ma'] = close.iloc[-1] > indicators['ma']
        
        # 3. Bollinger Bands (volatilita)
        bb = ta.volatility.BollingerBands(close, window=self.bb_period)
        indicators['bb_upper'] = bb.bollinger_hband().iloc[-1]
        indicators['bb_lower'] = bb.bollinger_lband().iloc[-1]
        indicators['bb_middle'] = bb.bollinger_mavg().iloc[-1]
        
        # 4. MACD (momentum)
        macd = ta.trend.MACD(close)
        indicators['macd'] = macd.macd().iloc[-1]
        indicators['macd_signal'] = macd.macd_signal().iloc[-1]
        indicators['macd_bullish'] = indicators['macd'] > indicators['macd_signal']
        
        # 5. Volume (pokud dostupné)
        if volume is not None:
            indicators['volume_ma'] = volume.rolling(window=10).mean().iloc[-1]
            indicators['high_volume'] = volume.iloc[-1] > indicators['volume_ma'] * 1.2
        else:
            indicators['high_volume'] = True  # Default pokud není volume
        
        # 6. Price action
        indicators['current_price'] = close.iloc[-1]
        indicators['prev_price'] = close.iloc[-2] if len(close) > 1 else close.iloc[-1]
        indicators['price_rising'] = indicators['current_price'] > indicators['prev_price']
        
        return indicators
    
    def analyze_signal(self, symbol: str, data: pd.DataFrame) -> Dict:
        """Vylepšená analýza signálu"""
        try:
            if len(data) < max(self.rsi_period, self.ma_period, self.bb_period):
                return {
                    'action': 'HOLD',
                    'reason': 'Nedostatek dat',
                    'confidence': 0
                }
            
            indicators = self.calculate_indicators(data)
            
            # Kontrola pozice
            has_position = symbol in self.positions and self.positions[symbol]['quantity'] != 0
            
            # NÁKUPNÍ SIGNÁLY (všechny musí být splněny)
            buy_signals = []
            
            # 1. RSI oversold - AGRESIVNÍ pro volatilní trh
            if indicators['rsi'] <= 40:
                buy_signals.append(f"RSI {indicators['rsi']:.1f} ≤ 40")
            
            # 2. Cena nad MA (uptrend)
            if indicators['price_above_ma']:
                buy_signals.append("Cena nad MA20 (uptrend)")
            
            # 3. MACD bullish
            if indicators['macd_bullish']:
                buy_signals.append("MACD bullish")
            
            # 4. Vysoký volume
            if indicators['high_volume']:
                buy_signals.append("Vysoký volume")
            
            # PRODEJNÍ SIGNÁLY
            sell_signals = []
            
            # 1. RSI overbought - AGRESIVNÍ pro volatilní trh
            if indicators['rsi'] >= 60:
                sell_signals.append(f"RSI {indicators['rsi']:.1f} ≥ 60")
            
            # 2. Cena pod MA (downtrend)
            if not indicators['price_above_ma']:
                sell_signals.append("Cena pod MA20 (downtrend)")
            
            # 3. MACD bearish
            if not indicators['macd_bullish']:
                sell_signals.append("MACD bearish")
            
            # ROZHODNUTÍ
            action = 'HOLD'
            reason = f"RSI {indicators['rsi']:.1f} v neutrální zóně"
            confidence = 0
            
            # BUY - potřebujeme alespoň 3 ze 4 signálů
            if len(buy_signals) >= 3 and not has_position:
                action = 'BUY'
                reason = f"STRONG BUY: {', '.join(buy_signals[:3])}"
                confidence = len(buy_signals) * 25
            
            # SELL - potřebujeme alespoň 2 ze 3 signálů
            elif len(sell_signals) >= 2 and has_position:
                action = 'SELL'
                reason = f"SELL: {', '.join(sell_signals[:2])}"
                confidence = len(sell_signals) * 30
            
            # WEAK BUY - jen RSI + 1 další signál - AGRESIVNÍ
            elif indicators['rsi'] <= 40 and len(buy_signals) >= 2 and not has_position:
                action = 'BUY'
                reason = f"WEAK BUY: {', '.join(buy_signals[:2])}"
                confidence = 50
            
            return {
                'action': action,
                'reason': reason,
                'rsi': indicators['rsi'],
                'confidence': confidence,
                'price': indicators['current_price'],
                'indicators': indicators
            }
            
        except Exception as e:
            logger.error(f"Chyba v enhanced analýze pro {symbol}: {e}")
            return {
                'action': 'HOLD',
                'reason': f'Chyba: {str(e)}',
                'confidence': 0
            }
    
    def update_position(self, symbol: str, side: str, quantity: float, price: float):
        """Aktualizace pozice"""
        if symbol not in self.positions:
            self.positions[symbol] = {'quantity': 0, 'entry_price': 0, 'side': None}
        
        if side == 'buy':
            self.positions[symbol] = {
                'quantity': quantity,
                'entry_price': price,
                'side': 'long'
            }
        elif side == 'sell':
            self.positions[symbol] = {
                'quantity': 0,
                'entry_price': 0,
                'side': None
            }
    
    def get_position(self, symbol: str) -> Dict:
        """Získání pozice"""
        return self.positions.get(symbol, {
            'quantity': 0,
            'entry_price': 0,
            'side': None
        })
