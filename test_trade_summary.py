#!/usr/bin/env python3
"""
📊 TEST SUMARIZACE OBCHODŮ
Testování nové funkce pro sumarizaci obchodů s P&L
"""

from colors import print_banner, success, error, highlight, warning

def test_trade_summary_examples():
    """Test různých příkladů sumarizace obchodů"""
    print_banner("📊 TEST SUMARIZACE OBCHODŮ")
    
    print(f"\n🧮 {highlight('PŘÍKLADY SUMARIZACE OBCHODŮ')}:")
    print("=" * 70)
    
    examples = [
        {
            'name': 'Pouze nákup ADA',
            'trades': [
                {'symbol': 'ADA/USDT', 'side': 'BUY', 'quantity': 5255.02, 'price': 0.6169, 'pnl': None}
            ],
            'expected': '📋 ✅ Provedeno 1 obchodů: ADA/USDT nákup -3242'
        },
        {
            'name': 'Ziskový prodej ADA',
            'trades': [
                {'symbol': 'ADA/USDT', 'side': 'SELL', 'quantity': 5255.02, 'price': 0.6200, 'pnl': 16.31}
            ],
            'expected': '📋 ✅ Provedeno 1 obchodů: ADA/USDT prodej +16 | Celkem P&L: +16 USDT'
        },
        {
            'name': 'Ztrátový prodej ADA',
            'trades': [
                {'symbol': 'ADA/USDT', 'side': 'SELL', 'quantity': 5255.02, 'price': 0.6100, 'pnl': -36.29}
            ],
            'expected': '📋 ✅ Provedeno 1 obchodů: ADA/USDT prodej -36 | Celkem P&L: -36 USDT'
        },
        {
            'name': 'Kompletní cyklus - nákup a prodej',
            'trades': [
                {'symbol': 'ADA/USDT', 'side': 'BUY', 'quantity': 5255.02, 'price': 0.6169, 'pnl': None},
                {'symbol': 'ADA/USDT', 'side': 'SELL', 'quantity': 5255.02, 'price': 0.6200, 'pnl': 16.31}
            ],
            'expected': '📋 ✅ Provedeno 2 obchodů: ADA/USDT nákup -3242, ADA/USDT prodej +16 | Celkem P&L: +16 USDT'
        },
        {
            'name': 'Více symbolů - smíšené výsledky',
            'trades': [
                {'symbol': 'BTC/USDT', 'side': 'BUY', 'quantity': 0.03, 'price': 104000, 'pnl': None},
                {'symbol': 'ETH/USDT', 'side': 'SELL', 'quantity': 1.2, 'price': 2550, 'pnl': 24.00},
                {'symbol': 'ADA/USDT', 'side': 'SELL', 'quantity': 5000, 'price': 0.62, 'pnl': -15.50}
            ],
            'expected': '📋 ✅ Provedeno 3 obchodů: BTC/USDT nákup -3120, ETH/USDT prodej +24, ADA/USDT prodej -16 | Celkem P&L: +9 USDT'
        },
        {
            'name': 'Emergency sell s malou ztrátou',
            'trades': [
                {'symbol': 'ADA/USDT', 'side': 'SELL', 'quantity': 5255.02, 'price': 0.6169, 'pnl': 0.00}
            ],
            'expected': '📋 ✅ Provedeno 1 obchodů: ADA/USDT prodej +0 | Celkem P&L: +0 USDT'
        }
    ]
    
    for example in examples:
        print(f"\n📊 {highlight(example['name'])}:")
        
        # Simulace obchodů
        trades_summary = []
        total_pnl = 0
        
        for trade in example['trades']:
            symbol = trade['symbol']
            side = trade['side']
            quantity = trade['quantity']
            price = trade['price']
            pnl = trade.get('pnl', 0)
            
            if side == 'BUY':
                cost = quantity * price
                trades_summary.append(f"{symbol} nákup -{cost:.0f}")
            elif side == 'SELL':
                if pnl is not None:
                    total_pnl += pnl
                    pnl_sign = "+" if pnl >= 0 else ""
                    trades_summary.append(f"{symbol} prodej {pnl_sign}{pnl:.0f}")
        
        # Vytvoření finálního souhrnu
        if trades_summary:
            result = ", ".join(trades_summary)
            if total_pnl != 0:
                pnl_sign = "+" if total_pnl >= 0 else ""
                result += f" | Celkem P&L: {pnl_sign}{total_pnl:.0f} USDT"
            final_summary = f"📋 ✅ Provedeno {len(example['trades'])} obchodů: {result}"
        
        print(f"   📝 Výsledek: {final_summary}")
        print(f"   📋 Očekáváno: {example['expected']}")
        
        # Porovnání
        if final_summary == example['expected']:
            print(f"   ✅ {success('SPRÁVNĚ')}")
        else:
            print(f"   ❌ {error('ROZDÍL')}")

def show_benefits():
    """Výhody nové sumarizace"""
    print(f"\n🎯 {highlight('VÝHODY NOVÉ SUMARIZACE')}:")
    print("=" * 70)
    
    benefits = [
        "📊 **Okamžitý přehled** o všech obchodech v cyklu",
        "💰 **P&L informace** u každého prodeje",
        "📈 **Celkový P&L** za všechny obchody",
        "🔍 **Jasné rozlišení** nákupů (-částka) a prodejů (+P&L)",
        "📋 **Kompaktní formát** - vše na jednom řádku",
        "🎯 **Rychlé posouzení** ziskovosti obchodů",
        "📊 **Lepší sledování** výkonnosti bota",
        "💡 **Snadná identifikace** problémových obchodů"
    ]
    
    for benefit in benefits:
        print(f"   ✅ {benefit}")

def show_format_explanation():
    """Vysvětlení formátu"""
    print(f"\n📋 {highlight('VYSVĚTLENÍ FORMÁTU SUMARIZACE')}:")
    print("=" * 70)
    
    format_rules = {
        "🚀 NÁKUPY": [
            "Formát: 'SYMBOL nákup -částka'",
            "Příklad: 'ADA/USDT nákup -3242'",
            "Význam: Utraceno 3242 USDT za nákup ADA",
            "Záporné číslo = výdaj"
        ],
        "💰 PRODEJE": [
            "Formát: 'SYMBOL prodej +/-P&L'",
            "Příklad: 'ADA/USDT prodej +16'",
            "Význam: Zisk 16 USDT z prodeje ADA",
            "Kladné = zisk, záporné = ztráta"
        ],
        "📊 CELKOVÝ P&L": [
            "Formát: '| Celkem P&L: +/-částka USDT'",
            "Příklad: '| Celkem P&L: +25 USDT'",
            "Význam: Celkový zisk/ztráta za cyklus",
            "Zobrazuje se pouze při prodejích"
        ],
        "🔢 POČET OBCHODŮ": [
            "Formát: 'Provedeno X obchodů:'",
            "Příklad: 'Provedeno 3 obchodů:'",
            "Význam: Celkový počet obchodů v cyklu",
            "Zahrnuje nákupy i prodeje"
        ]
    }
    
    for category, rules in format_rules.items():
        print(f"\n{category}:")
        for rule in rules:
            print(f"   • {rule}")

def show_real_examples():
    """Reálné příklady z logů"""
    print(f"\n📝 {highlight('REÁLNÉ PŘÍKLADY Z VAŠICH LOGŮ')}:")
    print("=" * 70)
    
    real_examples = [
        {
            'scenario': 'Váš aktuální nákup ADA',
            'old_format': '✅ Provedeno 1 obchodů v tomto cyklu',
            'new_format': '📋 ✅ Provedeno 1 obchodů: ADA/USDT nákup -3242',
            'improvement': 'Vidíte kolik jste utratili za ADA'
        },
        {
            'scenario': 'Budoucí ziskový prodej ADA',
            'old_format': '✅ Provedeno 1 obchodů v tomto cyklu',
            'new_format': '📋 ✅ Provedeno 1 obchodů: ADA/USDT prodej +50 | Celkem P&L: +50 USDT',
            'improvement': 'Okamžitě vidíte zisk z prodeje'
        },
        {
            'scenario': 'Emergency sell s malou ztrátou',
            'old_format': '✅ Provedeno 1 obchodů v tomto cyklu',
            'new_format': '📋 ✅ Provedeno 1 obchodů: ADA/USDT prodej -10 | Celkem P&L: -10 USDT',
            'improvement': 'Jasně vidíte že emergency sell způsobil malou ztrátu'
        },
        {
            'scenario': 'Aktivní cyklus s více obchody',
            'old_format': '✅ Provedeno 3 obchodů v tomto cyklu',
            'new_format': '📋 ✅ Provedeno 3 obchodů: BTC/USDT nákup -3000, ETH/USDT prodej +25, ADA/USDT prodej -5 | Celkem P&L: +20 USDT',
            'improvement': 'Kompletní přehled všech obchodů a celkového výsledku'
        }
    ]
    
    for example in real_examples:
        print(f"\n📊 {example['scenario']}:")
        print(f"   📜 Starý formát: {warning(example['old_format'])}")
        print(f"   🆕 Nový formát: {success(example['new_format'])}")
        print(f"   💡 Zlepšení: {example['improvement']}")

def main():
    """Hlavní funkce"""
    print("📊 TEST SUMARIZACE OBCHODŮ S P&L")
    print("=" * 70)
    
    test_trade_summary_examples()
    show_benefits()
    show_format_explanation()
    show_real_examples()
    
    print(f"\n🎉 {success('SUMARIZACE OBCHODŮ IMPLEMENTOVÁNA!')}")
    print(f"   📊 Detailní P&L informace u každého obchodu")
    print(f"   💰 Okamžitý přehled ziskovosti")
    print(f"   📋 Kompaktní formát na jednom řádku")
    print(f"   🎯 Lepší sledování výkonnosti bota")
    
    print(f"\n🚀 {highlight('PŘÍŠTÍ SPUŠTĚNÍ UKÁŽE')}:")
    print(f"   📋 ✅ Provedeno X obchodů: SYMBOL akce ±částka")
    print(f"   💰 Celkový P&L za každý cyklus")
    print(f"   📊 Jasné rozlišení nákupů a prodejů")

if __name__ == "__main__":
    main()
