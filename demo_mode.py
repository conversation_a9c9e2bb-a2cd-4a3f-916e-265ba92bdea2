#!/usr/bin/env python3
"""
Demo režim pro trading bota
Simuluje obchodování bez nutnosti API klíčů
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import time
from datetime import datetime, timedelta
import os
import sys

# Přidání aktuálního adresáře do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from strategy import RSIStrategy
from risk_management import RiskManager
from logger import logger
from utils import print_backtest_summary, plot_backtest_results, save_backtest_results

class DemoBot:
    """Demo verze trading bota bez nutnosti API klíčů"""
    
    def __init__(self):
        self.config = Config
        self.strategy = RSIStrategy(
            rsi_period=Config.RSI_PERIOD,
            oversold=Config.RSI_OVERSOLD,
            overbought=Config.RSI_OVERBOUGHT
        )
        self.risk_manager = RiskManager(
            max_position_size=Config.MAX_POSITION_SIZE,
            stop_loss_pct=Config.STOP_LOSS_PERCENT,
            take_profit_pct=Config.TAKE_PROFIT_PERCENT
        )
        self.balance = 10000.0  # Demo balance
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        
    def generate_demo_data(self, symbol, days=30, volatility=0.02):
        """Generování demo dat pro testování"""
        logger.info(f"Generování demo dat pro {symbol}...")
        
        # Počáteční cena
        if symbol == 'BTC/USDT':
            start_price = 50000
        elif symbol == 'ETH/USDT':
            start_price = 3000
        elif symbol == 'ADA/USDT':
            start_price = 0.5
        else:
            start_price = 100
        
        # Generování dat
        periods = days * 24  # Hodinové svíčky
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='1H')
        
        # Simulace ceny s random walkem
        np.random.seed(42)  # Pro konzistentní výsledky
        returns = np.random.normal(0, volatility, periods)
        price_changes = 1 + returns
        prices = start_price * np.cumprod(price_changes)
        
        # Vytvoření OHLCV dat
        data = pd.DataFrame({
            'timestamp': dates,
            'open': prices * 0.998,
            'high': prices * 1.005,
            'low': prices * 0.995,
            'close': prices,
            'volume': np.random.randint(100, 10000, periods)
        })
        
        data.set_index('timestamp', inplace=True)
        return data
    
    def run_demo(self, days=30):
        """Spuštění demo režimu"""
        logger.info("=== Spouštění Demo Režimu ===")
        logger.info(f"Počáteční balance: {self.balance} USDT")
        
        # Generování dat pro všechny symboly
        data = {}
        for symbol in Config.SYMBOLS:
            data[symbol] = self.generate_demo_data(symbol, days)
        
        # Simulace obchodování
        for day in range(days):
            current_date = datetime.now() - timedelta(days=days-day)
            logger.info(f"=== Den {day+1}/{days} - {current_date.strftime('%Y-%m-%d')} ===")
            
            # Zpracování každého symbolu
            for symbol in Config.SYMBOLS:
                # Získání dat pro aktuální den
                current_data = data[symbol].iloc[:day*24+24]
                if len(current_data) < Config.RSI_PERIOD:
                    continue
                
                # Aktuální cena
                current_price = float(current_data['close'].iloc[-1])
                
                # Kontrola exit podmínek (SL/TP)
                if symbol in self.positions:
                    position = self.positions[symbol]
                    entry_price = position['entry_price']
                    
                    # Aktualizace nerealizovaného P&L
                    if position['side'] == 'long':
                        unrealized_pnl = (current_price - entry_price) * position['quantity']
                    else:
                        unrealized_pnl = (entry_price - current_price) * position['quantity']
                    
                    position['unrealized_pnl'] = unrealized_pnl
                    
                    # Kontrola SL/TP
                    exit_check = self.risk_manager.check_exit_conditions(symbol, current_price)
                    if exit_check['should_exit']:
                        self.close_position(symbol, current_price, exit_check['reason'])
                        continue
                
                # Analýza signálu
                signal = self.strategy.analyze_signal(symbol, current_data)
                
                if signal['action'] == 'BUY' and symbol not in self.positions:
                    self.open_position(symbol, current_price, 'BUY', signal['reason'])
                elif signal['action'] == 'SELL' and symbol in self.positions:
                    self.close_position(symbol, current_price, signal['reason'])
            
            # Denní souhrn
            total_value = self.balance
            for symbol, position in self.positions.items():
                total_value += position['value'] + position['unrealized_pnl']
            
            self.equity_curve.append({
                'date': current_date,
                'balance': self.balance,
                'total_value': total_value
            })
            
            logger.info(f"Balance: {self.balance:.2f} USDT")
            logger.info(f"Portfolio hodnota: {total_value:.2f} USDT")
            logger.info(f"Otevřené pozice: {len(self.positions)}")
            
            # Pauza pro lepší čitelnost
            time.sleep(0.5)
        
        # Závěrečný souhrn
        self.print_summary()
    
    def open_position(self, symbol, price, side, reason):
        """Otevření pozice"""
        # Výpočet velikosti pozice
        quantity = self.risk_manager.calculate_position_size(self.balance, price, symbol)
        value = quantity * price
        
        # Kontrola dostatku prostředků
        if value > self.balance * 0.95:
            logger.warning(f"Nedostatek prostředků pro {symbol} - {value:.2f} > {self.balance * 0.95:.2f}")
            return
        
        # Otevření pozice
        self.balance -= value
        
        # Výpočet SL/TP
        sl_tp = self.risk_manager.calculate_stop_loss_take_profit(price, side.lower())
        
        # Přidání pozice
        self.positions[symbol] = {
            'entry_price': price,
            'quantity': quantity,
            'value': value,
            'side': 'long' if side == 'BUY' else 'short',
            'entry_time': datetime.now(),
            'stop_loss': sl_tp['stop_loss'],
            'take_profit': sl_tp['take_profit'],
            'unrealized_pnl': 0.0
        }
        
        # Log
        logger.info(f"OTEVŘENO: {side} {quantity:.6f} {symbol} @ {price:.2f} | Hodnota: {value:.2f} USDT | {reason}")
        logger.info(f"SL: {sl_tp['stop_loss']:.2f} | TP: {sl_tp['take_profit']:.2f}")
    
    def close_position(self, symbol, price, reason):
        """Uzavření pozice"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        entry_price = position['entry_price']
        quantity = position['quantity']
        value = quantity * price
        
        # Výpočet P&L
        if position['side'] == 'long':
            pnl = (price - entry_price) * quantity
        else:
            pnl = (entry_price - price) * quantity
        
        # Aktualizace balance
        self.balance += value
        
        # Záznam obchodu
        trade = {
            'symbol': symbol,
            'entry_time': position['entry_time'],
            'exit_time': datetime.now(),
            'entry_price': entry_price,
            'exit_price': price,
            'quantity': quantity,
            'side': position['side'],
            'pnl': pnl,
            'pnl_percent': (pnl / position['value']) * 100,
            'reason': reason
        }
        
        self.trades.append(trade)
        
        # Log
        logger.info(f"UZAVŘENO: SELL {quantity:.6f} {symbol} @ {price:.2f} | P&L: {pnl:+.2f} USDT ({trade['pnl_percent']:+.2f}%) | {reason}")
        
        # Odebrání pozice
        del self.positions[symbol]
    
    def print_summary(self):
        """Výpis souhrnu obchodování"""
        logger.info("\n=== SOUHRN DEMO OBCHODOVÁNÍ ===")
        
        # Základní metriky
        initial_balance = 10000.0
        final_balance = self.balance
        for symbol, position in self.positions.items():
            final_balance += position['value'] + position['unrealized_pnl']
        
        total_return = ((final_balance - initial_balance) / initial_balance) * 100
        
        logger.info(f"Počáteční balance: {initial_balance:.2f} USDT")
        logger.info(f"Konečná balance: {final_balance:.2f} USDT")
        logger.info(f"Celkový výnos: {total_return:+.2f}%")
        
        # Statistiky obchodů
        if self.trades:
            total_trades = len(self.trades)
            winning_trades = len([t for t in self.trades if t['pnl'] > 0])
            losing_trades = len([t for t in self.trades if t['pnl'] < 0])
            
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            total_profit = sum([t['pnl'] for t in self.trades if t['pnl'] > 0])
            total_loss = sum([t['pnl'] for t in self.trades if t['pnl'] < 0])
            
            logger.info(f"Celkem obchodů: {total_trades}")
            logger.info(f"Vítězné obchody: {winning_trades} ({win_rate:.1f}%)")
            logger.info(f"Ztrátové obchody: {losing_trades}")
            logger.info(f"Celkový zisk: {total_profit:.2f} USDT")
            logger.info(f"Celková ztráta: {total_loss:.2f} USDT")
            
            # Nejlepší a nejhorší obchod
            if winning_trades > 0:
                best_trade = max(self.trades, key=lambda x: x['pnl'])
                logger.info(f"Nejlepší obchod: {best_trade['symbol']} | P&L: {best_trade['pnl']:.2f} USDT ({best_trade['pnl_percent']:.2f}%)")
            
            if losing_trades > 0:
                worst_trade = min(self.trades, key=lambda x: x['pnl'])
                logger.info(f"Nejhorší obchod: {worst_trade['symbol']} | P&L: {worst_trade['pnl']:.2f} USDT ({worst_trade['pnl_percent']:.2f}%)")
        
        # Otevřené pozice
        if self.positions:
            logger.info("\nOtevřené pozice:")
            for symbol, position in self.positions.items():
                logger.info(f"{symbol}: {position['quantity']:.6f} @ {position['entry_price']:.2f} | P&L: {position['unrealized_pnl']:+.2f} USDT")
        
        logger.info("=" * 40)
    
    def plot_equity_curve(self):
        """Vykreslení equity křivky"""
        if not self.equity_curve:
            logger.warning("Žádná data pro vykreslení equity křivky")
            return
        
        df = pd.DataFrame(self.equity_curve)
        
        plt.figure(figsize=(12, 6))
        plt.plot(df['date'], df['total_value'], label='Portfolio hodnota', color='blue')
        plt.axhline(y=10000, color='r', linestyle='--', label='Počáteční balance')
        
        plt.title('Demo Trading - Equity Curve')
        plt.xlabel('Datum')
        plt.ylabel('Hodnota (USDT)')
        plt.legend()
        plt.grid(True)
        
        # Uložení grafu
        plt.savefig('demo_equity_curve.png')
        logger.info("Graf uložen jako demo_equity_curve.png")
        
        # Zobrazení grafu
        plt.show()

def run_demo_mode(days=30):
    """Spuštění demo režimu"""
    print("\n" + "=" * 50)
    print("           DEMO REŽIM")
    print("=" * 50)
    print("Simulace obchodování bez API klíčů")
    print(f"Období: {days} dní")
    print("=" * 50)
    
    bot = DemoBot()
    bot.run_demo(days)
    
    try:
        bot.plot_equity_curve()
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu: {e}")
    
    return True

if __name__ == "__main__":
    days = 30
    if len(sys.argv) > 1:
        try:
            days = int(sys.argv[1])
        except ValueError:
            pass
    
    run_demo_mode(days)
