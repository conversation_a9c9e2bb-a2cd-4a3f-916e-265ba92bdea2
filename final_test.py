#!/usr/bin/env python3

import sys
import os

print("🚀 FINAL TEST - AGRESIVNÍ BOT")
print("=" * 50)

try:
    # Test ccxt
    import ccxt
    print("✅ ccxt imported")
    
    # Test exchange
    exchange = ccxt.binance({
        'sandbox': True,
        'enableRateLimit': True,
    })
    print("✅ Binance testnet connected")
    
    # Test BTC price
    ticker = exchange.fetch_ticker('BTC/USDT')
    btc_price = ticker['last']
    print(f"📊 BTC/USDT: ${btc_price:,.2f}")
    
    # Test ETH price
    ticker_eth = exchange.fetch_ticker('ETH/USDT')
    eth_price = ticker_eth['last']
    print(f"📊 ETH/USDT: ${eth_price:,.2f}")
    
    # Test pandas
    import pandas as pd
    print("✅ pandas imported")
    
    # Test ta
    import ta
    print("✅ ta imported")
    
    # Test RSI calculation
    ohlcv = exchange.fetch_ohlcv('BTC/USDT', '5m', limit=30)
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    
    rsi_indicator = ta.momentum.RSIIndicator(close=df['close'], window=14)
    df['rsi'] = rsi_indicator.rsi()
    current_rsi = df['rsi'].iloc[-1]
    
    print(f"📈 BTC RSI: {current_rsi:.2f}")
    
    # AGRESIVNÍ signál test
    print(f"\n🎯 AGRESIVNÍ STRATEGIE (40/60):")
    
    if current_rsi <= 40:
        signal = "🟢 AGRESIVNÍ BUY SIGNÁL!"
        print(f"BTC: {signal}")
        print(f"   📝 RSI {current_rsi:.2f} ≤ 40 (oversold)")
    elif current_rsi >= 60:
        signal = "🔴 AGRESIVNÍ SELL SIGNÁL!"
        print(f"BTC: {signal}")
        print(f"   📝 RSI {current_rsi:.2f} ≥ 60 (overbought)")
    else:
        signal = "🟡 HOLD"
        print(f"BTC: {signal}")
        print(f"   📝 RSI {current_rsi:.2f} v neutrální zóně (40-60)")
    
    # Test ETH RSI
    ohlcv_eth = exchange.fetch_ohlcv('ETH/USDT', '5m', limit=30)
    df_eth = pd.DataFrame(ohlcv_eth, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    
    rsi_indicator_eth = ta.momentum.RSIIndicator(close=df_eth['close'], window=14)
    df_eth['rsi'] = rsi_indicator_eth.rsi()
    current_rsi_eth = df_eth['rsi'].iloc[-1]
    
    print(f"\n📈 ETH RSI: {current_rsi_eth:.2f}")
    
    if current_rsi_eth <= 40:
        signal_eth = "🟢 AGRESIVNÍ BUY SIGNÁL!"
        print(f"ETH: {signal_eth}")
        print(f"   📝 RSI {current_rsi_eth:.2f} ≤ 40 (oversold)")
    elif current_rsi_eth >= 60:
        signal_eth = "🔴 AGRESIVNÍ SELL SIGNÁL!"
        print(f"ETH: {signal_eth}")
        print(f"   📝 RSI {current_rsi_eth:.2f} ≥ 60 (overbought)")
    else:
        signal_eth = "🟡 HOLD"
        print(f"ETH: {signal_eth}")
        print(f"   📝 RSI {current_rsi_eth:.2f} v neutrální zóně (40-60)")
    
    print(f"\n🎉 VŠECHNO FUNGUJE PERFEKTNĚ!")
    print(f"✅ Moduly nainstalované")
    print(f"✅ API připojení funkční")
    print(f"✅ RSI výpočet funguje")
    print(f"✅ Agresivní strategie 40/60 aktivní")
    
    print(f"\n🚀 BOT JE PŘIPRAVEN!")
    print(f"Spusťte: python main.py --mode live")
    print(f"Nebo: python simple_bot.py")
    
except Exception as e:
    print(f"❌ CHYBA: {e}")
    import traceback
    traceback.print_exc()

print(f"\n✅ Test dokončen")
