# 🔑 Nastavení API klíčů

Tento návod vám pomůže získat API klíče pro Binance a Bybit burzy.

## 🟡 Binance API klíče

### Testnet (Demo účet) - DOPORUČENO PRO ZAČÁTEČNÍKY

1. **Přejděte na Binance Testnet**:
   - URL: https://testnet.binancefuture.com/
   - Přihlaste se pomocí GitHub účtu

2. **Získání API klíčů**:
   - Klikněte na ikonu profilu (vpravo nahoře)
   - Vyberte "API Management"
   - Klikněte "Create API"
   - <PERSON>adej<PERSON> název (např. "TradingBot")
   - Zkopírujte API Key a Secret Key

3. **Nastavení oprávnění**:
   - ✅ Enable Reading
   - ✅ Enable Futures
   - ❌ Enable Withdrawals (NIKDY nepovolujte!)

### Live účet (Reálné peníze) - POUZE PRO ZKUŠENÉ

⚠️ **POZOR**: Používejte pouze s penězi, které si můžete dovolit ztratit!

1. **Přejděte na Binance**:
   - URL: https://www.binance.com/
   - Přihlaste se do svého účtu

2. **Získání API klíčů**:
   - Profil → API Management
   - Create API
   - Zadejte název a dokončete 2FA
   - Zkopírujte klíče

3. **Nastavení oprávnění**:
   - ✅ Enable Reading
   - ✅ Enable Futures
   - ❌ Enable Withdrawals

## 🔵 Bybit API klíče

### Testnet (Demo účet)

1. **Přejděte na Bybit Testnet**:
   - URL: https://testnet.bybit.com/
   - Zaregistrujte se nebo se přihlaste

2. **Získání API klíčů**:
   - Account → API Management
   - Create New Key
   - Zadejte název
   - Zkopírujte API Key a Secret

3. **Nastavení oprávnění**:
   - ✅ Read-Write
   - ✅ Derivatives
   - ❌ Withdraw

### Live účet

1. **Přejděte na Bybit**:
   - URL: https://www.bybit.com/
   - Přihlaste se do svého účtu

2. **Získání API klíčů**:
   - Account → API Management
   - Create New Key
   - Dokončete bezpečnostní ověření
   - Zkopírujte klíče

## 🛡️ Bezpečnostní doporučení

### ✅ CO DĚLAT:
- Používejte pouze testnet pro začátky
- Nikdy nesdílejte své API klíče
- Používejte silná hesla a 2FA
- Pravidelně kontrolujte aktivitu účtu
- Začněte s malým kapitálem

### ❌ CO NEDĚLAT:
- Nepovolujte withdrawal oprávnění
- Nesdílejte klíče na internetu
- Neukládejte klíče v nezabezpečených souborech
- Neobchodujte s penězi, které si nemůžete dovolit ztratit

## 📝 Konfigurace v .env

Po získání API klíčů je vložte do `.env` souboru:

```env
# Pro Binance testnet
EXCHANGE=binance
API_KEY=your_binance_testnet_api_key
API_SECRET=your_binance_testnet_secret
SANDBOX=True

# Pro Bybit testnet
EXCHANGE=bybit
API_KEY=your_bybit_testnet_api_key
API_SECRET=your_bybit_testnet_secret
SANDBOX=True
```

## 🧪 Test připojení

Po nastavení klíčů otestujte připojení:

```bash
python main.py --mode test
```

Měli byste vidět:
```
✅ Připojení úspěšné!
✅ Dostupný zůstatek: 10000.00 USDT
✅ Burza: binance (testnet)
```

## 🆘 Řešení problémů

### Chyba: "Invalid API key"
- Zkontrolujte, že jste zkopírovali klíče správně
- Ujistěte se, že používáte správnou burzu (binance/bybit)
- Pro testnet používejte testnet klíče

### Chyba: "Permission denied"
- Zkontrolujte oprávnění API klíčů
- Ujistěte se, že máte povoleno "Futures" trading

### Chyba: "IP not whitelisted"
- V nastavení API klíčů povolte všechny IP adresy
- Nebo přidejte svou IP adresu do whitelistu

## 📞 Podpora

Pokud máte problémy:
1. Zkontrolujte logy v `logs/trading_bot.log`
2. Ověřte konfiguraci v `.env`
3. Spusťte test: `python main.py --mode test`

---

**Důležité**: Vždy začněte s demo účtem a důkladně otestujte bota před použitím reálných peněz!
