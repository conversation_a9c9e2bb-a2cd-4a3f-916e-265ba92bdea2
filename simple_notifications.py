#!/usr/bin/env python3
"""
Jednoduchý notifikační systém
"""

import requests
import json
from datetime import datetime
from typing import Dict

class SimpleNotifier:
    """Jednoduchý notifikační systém pro Discord"""
    
    def __init__(self, discord_webhook_url=""):
        self.discord_webhook = discord_webhook_url
        self.enabled = bool(discord_webhook_url)
    
    def send_discord(self, message: str, color: int = 0x00ff00, title: str = "🤖 Trading Bot"):
        """Odeslání Discord notifikace"""
        if not self.enabled:
            return False
        
        try:
            embed = {
                "title": title,
                "description": message,
                "color": color,
                "timestamp": datetime.now().isoformat(),
                "footer": {"text": "Enhanced Trading Bot v2.0"}
            }
            
            data = {"embeds": [embed]}
            
            response = requests.post(self.discord_webhook, json=data, timeout=5)
            return response.status_code == 204
        except Exception as e:
            print(f"Discord notifikace selhala: {e}")
            return False
    
    def notify_trade(self, symbol: str, action: str, price: float, quantity: float, 
                    reason: str, confidence: int):
        """Notifikace o obchodu"""
        if action == 'BUY':
            emoji = "🟢"
            color = 0x00ff00
            title = f"🚀 NÁKUP {symbol}"
        else:
            emoji = "🔴"
            color = 0xff0000
            title = f"💰 PRODEJ {symbol}"
        
        message = f"""
{emoji} **{action} {symbol}**
💰 **Cena:** ${price:,.2f}
📊 **Množství:** {quantity:.6f}
🎯 **Confidence:** {confidence}%
📝 **Důvod:** {reason}
⏰ **Čas:** {datetime.now().strftime('%H:%M:%S')}
"""
        
        self.send_discord(message, color, title)
    
    def notify_error(self, error_message: str):
        """Notifikace o chybě"""
        message = f"❌ **CHYBA:**\n{error_message}\n⏰ {datetime.now().strftime('%H:%M:%S')}"
        self.send_discord(message, 0xff0000, "🚨 ERROR")
    
    def notify_startup(self, symbols: list, strategy_info: str):
        """Notifikace o spuštění bota"""
        message = f"""
🚀 **BOT SPUŠTĚN**
📊 **Symboly:** {', '.join(symbols)}
🎯 **Strategie:** {strategy_info}
⏰ **Čas:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        self.send_discord(message, 0x0099ff, "🤖 STARTUP")
    
    def notify_daily_summary(self, trades_count: int, pnl: float, balance: float):
        """Denní souhrn"""
        emoji = "📈" if pnl > 0 else "📉" if pnl < 0 else "➡️"
        color = 0x00ff00 if pnl > 0 else 0xff0000 if pnl < 0 else 0xffff00
        
        message = f"""
📊 **DENNÍ SOUHRN**
{emoji} **P&L:** ${pnl:+,.2f}
🔄 **Obchody:** {trades_count}
💰 **Balance:** ${balance:,.2f}
⏰ **Datum:** {datetime.now().strftime('%Y-%m-%d')}
"""
        
        self.send_discord(message, color, "📊 DAILY SUMMARY")

    def send_message(self, message: str, title: str = "🤖 Trading Bot", color: int = 0x0099ff):
        """Obecná metoda pro odeslání zprávy"""
        return self.send_discord(message, color, title)

# Globální instance
notifier = SimpleNotifier()

def setup_notifications(discord_webhook_url: str = ""):
    """Nastavení notifikací"""
    global notifier
    notifier = SimpleNotifier(discord_webhook_url)
    return notifier
