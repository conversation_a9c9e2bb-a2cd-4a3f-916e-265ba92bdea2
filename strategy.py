import pandas as pd
import ta
from typing import Dict, List, Tuple, Optional
from logger import logger

class RSIStrategy:
    """Enhanced RSI trading strategie s Moving Average filtrem"""

    def __init__(self, rsi_period=14, oversold=45, overbought=55, ma_period=20):
        self.rsi_period = rsi_period
        self.oversold = oversold  # SUPER AGRESIVNÍ 45 (OPRAVA ZTRÁT)
        self.overbought = overbought  # SUPER AGRESIVNÍ 55 (OPRAVA ZTRÁT)
        self.ma_period = ma_period
        self.positions = {}  # Sledování pozic pro každý symbol
    
    def calculate_rsi(self, prices: pd.Series) -> float:
        """Výpočet RSI"""
        try:
            rsi = ta.momentum.RSIIndicator(prices, window=self.rsi_period)
            return rsi.rsi().iloc[-1]
        except Exception as e:
            logger.error(f"Chyba při výpočtu RSI: {e}")
            return 50.0  # Neutráln<PERSON> hodnota

    def calculate_ma(self, prices: pd.Series) -> float:
        """Výpočet Moving Average"""
        try:
            ma = prices.rolling(window=self.ma_period).mean()
            return ma.iloc[-1]
        except Exception as e:
            logger.error(f"Chyba při výpočtu MA: {e}")
            return prices.iloc[-1]  # Fallback na aktuální cenu

    def calculate_macd(self, prices: pd.Series) -> Dict:
        """Výpočet MACD"""
        try:
            macd_indicator = ta.trend.MACD(prices)
            macd_line = macd_indicator.macd().iloc[-1]
            signal_line = macd_indicator.macd_signal().iloc[-1]

            return {
                'macd': macd_line,
                'signal': signal_line,
                'bullish': macd_line > signal_line,
                'histogram': macd_line - signal_line
            }
        except Exception as e:
            logger.error(f"Chyba při výpočtu MACD: {e}")
            return {
                'macd': 0,
                'signal': 0,
                'bullish': True,  # Neutrální
                'histogram': 0
            }
    
    def analyze_signal(self, symbol: str, ohlcv_data: pd.DataFrame) -> Dict:
        """Enhanced analýza signálu s Moving Average filtrem"""
        try:
            if len(ohlcv_data) < max(self.rsi_period, self.ma_period):
                return {
                    'action': 'HOLD',
                    'reason': 'Nedostatek dat pro analýzu',
                    'rsi': None,
                    'confidence': 0
                }

            # Výpočet indikátorů
            close_prices = ohlcv_data['close']
            current_rsi = self.calculate_rsi(close_prices)
            current_ma = self.calculate_ma(close_prices)
            macd_data = self.calculate_macd(close_prices)
            current_price = close_prices.iloc[-1]

            # Trend analysis
            price_above_ma = current_price > current_ma
            macd_bullish = macd_data['bullish']
            trend = "UPTREND" if price_above_ma else "DOWNTREND"
            momentum = "BULLISH" if macd_bullish else "BEARISH"

            # Určení akce
            action = 'HOLD'
            reason = f'RSI {current_rsi:.2f} v neutrální zóně'
            confidence = 0

            # Kontrola pozice
            has_position = symbol in self.positions and self.positions[symbol]['quantity'] != 0

            # 🚀 OPTIMALIZOVANÉ BUY SIGNÁLY (RSI ≤ 30)
            if current_rsi <= self.oversold and not has_position:
                if price_above_ma and macd_bullish:
                    action = 'BUY'
                    reason = f'🚀 TRIPLE BUY: RSI {current_rsi:.1f} ≤ {self.oversold} + {trend} + MACD {momentum}'
                    confidence = min(100, (self.oversold - current_rsi) * 6 + 50)  # Vyšší bonus pro triple signál
                elif price_above_ma:
                    action = 'BUY'
                    reason = f'✅ STRONG BUY: RSI {current_rsi:.1f} ≤ {self.oversold} + {trend}'
                    confidence = min(100, (self.oversold - current_rsi) * 5 + 35)
                elif current_rsi <= 20:  # EXTRÉMNĚ OVERSOLD - nakup i bez uptrend
                    action = 'BUY'
                    reason = f'🚀 EXTREME BUY: RSI {current_rsi:.1f} ≤ 20 (EXTRÉMNĚ OVERSOLD)'
                    confidence = min(100, (20 - current_rsi) * 8 + 60)
                elif macd_bullish:  # MACD bullish i bez uptrend
                    action = 'BUY'
                    reason = f'📈 MOMENTUM BUY: RSI {current_rsi:.1f} ≤ {self.oversold} + MACD {momentum}'
                    confidence = min(100, (self.oversold - current_rsi) * 4 + 30)
                else:
                    # Opatrný nákup pouze při RSI ≤ 30
                    action = 'BUY'
                    reason = f'⚡ OVERSOLD BUY: RSI {current_rsi:.1f} ≤ {self.oversold}'
                    confidence = min(100, (self.oversold - current_rsi) * 3 + 20)

            # 💰 PRIORITA: VÝBĚR ZISKU při dobrém P&L
            elif has_position and current_rsi >= 55:  # Mírně overbought = výběr zisku
                action = 'SELL'
                reason = f'💰 PROFIT TAKING: RSI {current_rsi:.1f} ≥ 55 - VÝBĚR ZISKU'
                confidence = min(100, (current_rsi - 55) * 4 + 70)

            # 🛑 STOP LOSS: Uzavření při větší ztrátě
            elif has_position and current_rsi <= 25:  # Velmi oversold = možná velká ztráta
                action = 'SELL'
                reason = f'🛑 STOP LOSS: RSI {current_rsi:.1f} ≤ 25 - OCHRANA PŘED ZTRÁTOU'
                confidence = min(100, (25 - current_rsi) * 5 + 80)

            # 🚨 KRITICKÉ: UZAVŘENÍ POZIC V DOWNTREND (OCHRANA KAPITÁLU)
            elif has_position and not price_above_ma and not macd_bullish:
                action = 'SELL'
                reason = f'🚨 EMERGENCY SELL: {trend} + MACD {momentum} - OCHRANA KAPITÁLU'
                confidence = 90

            # ENHANCED SELL LOGIC: RSI overbought + confirmations
            elif current_rsi >= self.overbought and has_position:
                if not price_above_ma and not macd_bullish:
                    action = 'SELL'
                    reason = f'TRIPLE SELL: RSI {current_rsi:.2f} + {trend} + MACD {momentum}'
                    confidence = min(100, (current_rsi - self.overbought) * 4 + 30)
                elif not price_above_ma or not macd_bullish:
                    action = 'SELL'
                    reason = f'STRONG SELL: RSI {current_rsi:.2f} + confirmace'
                    confidence = min(100, (current_rsi - self.overbought) * 3 + 15)
                else:
                    action = 'SELL'  # Prodej při overbought i bez confirmace
                    reason = f'SELL: RSI {current_rsi:.2f} >= {self.overbought} (overbought)'
                    confidence = min(100, (current_rsi - self.overbought) * 2)

            # WEAK BUY: RSI blízko oversold s alespoň jednou confirmací
            elif current_rsi <= self.oversold + 5 and not has_position:
                if price_above_ma and macd_bullish:
                    action = 'BUY'
                    reason = f'WEAK BUY: RSI {current_rsi:.2f} + {trend} + MACD {momentum}'
                    confidence = 60
                elif price_above_ma:
                    action = 'BUY'
                    reason = f'WEAK BUY: RSI {current_rsi:.2f} + {trend}'
                    confidence = 45

            # Log strategie s všemi indikátory
            enhanced_reason = f"{reason} | MA20: {current_ma:.2f} | MACD: {momentum}"
            logger.strategy_log(symbol, current_rsi, action, enhanced_reason)

            return {
                'action': action,
                'reason': enhanced_reason,
                'rsi': current_rsi,
                'confidence': confidence,
                'price': current_price,
                'ma': current_ma,
                'trend': trend,
                'price_above_ma': price_above_ma,
                'macd': macd_data,
                'momentum': momentum
            }
            
        except Exception as e:
            logger.error(f"Chyba při analýze signálu pro {symbol}: {e}")
            return {
                'action': 'HOLD',
                'reason': f'Chyba: {str(e)}',
                'rsi': None,
                'confidence': 0
            }
    
    def update_position(self, symbol: str, side: str, quantity: float, price: float):
        """Aktualizace pozice"""
        if symbol not in self.positions:
            self.positions[symbol] = {
                'quantity': 0,
                'entry_price': 0,
                'side': None
            }
        
        if side == 'buy':
            self.positions[symbol]['quantity'] = quantity
            self.positions[symbol]['entry_price'] = price
            self.positions[symbol]['side'] = 'long'
        elif side == 'sell':
            self.positions[symbol]['quantity'] = 0
            self.positions[symbol]['entry_price'] = 0
            self.positions[symbol]['side'] = None
    
    def get_position(self, symbol: str) -> Dict:
        """Získání aktuální pozice"""
        return self.positions.get(symbol, {
            'quantity': 0,
            'entry_price': 0,
            'side': None
        })
    
    def calculate_pnl(self, symbol: str, current_price: float) -> float:
        """Výpočet nerealizovaného P&L"""
        position = self.get_position(symbol)
        if position['quantity'] == 0:
            return 0.0
        
        if position['side'] == 'long':
            return (current_price - position['entry_price']) * position['quantity']
        else:
            return (position['entry_price'] - current_price) * position['quantity']
    
    def should_close_position(self, symbol: str, current_price: float, 
                            stop_loss_pct: float, take_profit_pct: float) -> Tuple[bool, str]:
        """Kontrola, zda zavřít pozici kvůli SL/TP"""
        position = self.get_position(symbol)
        if position['quantity'] == 0:
            return False, ""
        
        entry_price = position['entry_price']
        
        if position['side'] == 'long':
            # Stop Loss
            stop_loss_price = entry_price * (1 - stop_loss_pct / 100)
            if current_price <= stop_loss_price:
                return True, f"Stop Loss triggered: {current_price} <= {stop_loss_price:.4f}"
            
            # Take Profit
            take_profit_price = entry_price * (1 + take_profit_pct / 100)
            if current_price >= take_profit_price:
                return True, f"Take Profit triggered: {current_price} >= {take_profit_price:.4f}"
        
        return False, ""
