import pandas as pd
import ta
from typing import Dict, List, Tuple, Optional
from logger import logger

class RSIStrategy:
    """RSI trading strategie"""
    
    def __init__(self, rsi_period=14, oversold=30, overbought=70):
        self.rsi_period = rsi_period
        self.oversold = oversold
        self.overbought = overbought
        self.positions = {}  # Sledování pozic pro každý symbol
    
    def calculate_rsi(self, prices: pd.Series) -> float:
        """Výpočet RSI"""
        try:
            rsi = ta.momentum.RSIIndicator(prices, window=self.rsi_period)
            return rsi.rsi().iloc[-1]
        except Exception as e:
            logger.error(f"Chyba při výpočtu RSI: {e}")
            return 50.0  # Neutrální hodnota
    
    def analyze_signal(self, symbol: str, ohlcv_data: pd.DataFrame) -> Dict:
        """Analýza signálu pro daný symbol"""
        try:
            if len(ohlcv_data) < self.rsi_period:
                return {
                    'action': 'HOLD',
                    'reason': 'Nedostatek dat pro RSI',
                    'rsi': None,
                    'confidence': 0
                }
            
            # Výpočet RSI
            close_prices = ohlcv_data['close']
            current_rsi = self.calculate_rsi(close_prices)
            current_price = close_prices.iloc[-1]
            
            # Určení akce
            action = 'HOLD'
            reason = f'RSI {current_rsi:.2f} v neutrální zóně'
            confidence = 0
            
            # Kontrola pozice
            has_position = symbol in self.positions and self.positions[symbol]['quantity'] != 0
            
            if current_rsi <= self.oversold and not has_position:
                action = 'BUY'
                reason = f'RSI {current_rsi:.2f} <= {self.oversold} (oversold)'
                confidence = min(100, (self.oversold - current_rsi) * 2)
                
            elif current_rsi >= self.overbought and has_position:
                action = 'SELL'
                reason = f'RSI {current_rsi:.2f} >= {self.overbought} (overbought)'
                confidence = min(100, (current_rsi - self.overbought) * 2)
            
            # Log strategie
            logger.strategy_log(symbol, current_rsi, action, reason)
            
            return {
                'action': action,
                'reason': reason,
                'rsi': current_rsi,
                'confidence': confidence,
                'price': current_price
            }
            
        except Exception as e:
            logger.error(f"Chyba při analýze signálu pro {symbol}: {e}")
            return {
                'action': 'HOLD',
                'reason': f'Chyba: {str(e)}',
                'rsi': None,
                'confidence': 0
            }
    
    def update_position(self, symbol: str, side: str, quantity: float, price: float):
        """Aktualizace pozice"""
        if symbol not in self.positions:
            self.positions[symbol] = {
                'quantity': 0,
                'entry_price': 0,
                'side': None
            }
        
        if side == 'buy':
            self.positions[symbol]['quantity'] = quantity
            self.positions[symbol]['entry_price'] = price
            self.positions[symbol]['side'] = 'long'
        elif side == 'sell':
            self.positions[symbol]['quantity'] = 0
            self.positions[symbol]['entry_price'] = 0
            self.positions[symbol]['side'] = None
    
    def get_position(self, symbol: str) -> Dict:
        """Získání aktuální pozice"""
        return self.positions.get(symbol, {
            'quantity': 0,
            'entry_price': 0,
            'side': None
        })
    
    def calculate_pnl(self, symbol: str, current_price: float) -> float:
        """Výpočet nerealizovaného P&L"""
        position = self.get_position(symbol)
        if position['quantity'] == 0:
            return 0.0
        
        if position['side'] == 'long':
            return (current_price - position['entry_price']) * position['quantity']
        else:
            return (position['entry_price'] - current_price) * position['quantity']
    
    def should_close_position(self, symbol: str, current_price: float, 
                            stop_loss_pct: float, take_profit_pct: float) -> Tuple[bool, str]:
        """Kontrola, zda zavřít pozici kvůli SL/TP"""
        position = self.get_position(symbol)
        if position['quantity'] == 0:
            return False, ""
        
        entry_price = position['entry_price']
        
        if position['side'] == 'long':
            # Stop Loss
            stop_loss_price = entry_price * (1 - stop_loss_pct / 100)
            if current_price <= stop_loss_price:
                return True, f"Stop Loss triggered: {current_price} <= {stop_loss_price:.4f}"
            
            # Take Profit
            take_profit_price = entry_price * (1 + take_profit_pct / 100)
            if current_price >= take_profit_price:
                return True, f"Take Profit triggered: {current_price} >= {take_profit_price:.4f}"
        
        return False, ""
