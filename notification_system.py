#!/usr/bin/env python3
"""
Systém notifikací a monitoringu
"""

import smtplib
import requests
import json
from datetime import datetime
from typing import Dict, List
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

class NotificationSystem:
    """Systém pro notifikace a monitoring"""
    
    def __init__(self):
        self.email_config = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'email': '',  # Nastavit v .env
            'password': '',  # App password
            'to_email': ''
        }
        self.discord_webhook = ''  # Discord webhook URL
        self.telegram_config = {
            'bot_token': '',
            'chat_id': ''
        }
    
    def send_email(self, subject: str, message: str, is_html: bool = False):
        """Odeslání emailu"""
        try:
            msg = MimeMultipart()
            msg['From'] = self.email_config['email']
            msg['To'] = self.email_config['to_email']
            msg['Subject'] = subject
            
            msg.attach(MimeText(message, 'html' if is_html else 'plain'))
            
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['email'], self.email_config['password'])
            server.send_message(msg)
            server.quit()
            
            return True
        except Exception as e:
            print(f"Chyba při odesílání emailu: {e}")
            return False
    
    def send_discord(self, message: str, color: int = 0x00ff00):
        """Odeslání Discord notifikace"""
        if not self.discord_webhook:
            return False
        
        try:
            embed = {
                "title": "🤖 Trading Bot Alert",
                "description": message,
                "color": color,
                "timestamp": datetime.now().isoformat(),
                "footer": {"text": "Trading Bot v2.0"}
            }
            
            data = {"embeds": [embed]}
            
            response = requests.post(self.discord_webhook, json=data)
            return response.status_code == 204
        except Exception as e:
            print(f"Chyba při odesílání Discord zprávy: {e}")
            return False
    
    def send_telegram(self, message: str):
        """Odeslání Telegram zprávy"""
        if not self.telegram_config['bot_token']:
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.telegram_config['bot_token']}/sendMessage"
            data = {
                'chat_id': self.telegram_config['chat_id'],
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, data=data)
            return response.status_code == 200
        except Exception as e:
            print(f"Chyba při odesílání Telegram zprávy: {e}")
            return False
    
    def notify_trade(self, trade_info: Dict):
        """Notifikace o obchodu"""
        symbol = trade_info['symbol']
        action = trade_info['action']
        price = trade_info['price']
        quantity = trade_info['quantity']
        reason = trade_info['reason']
        
        if action == 'BUY':
            emoji = "🟢"
            color = 0x00ff00
        else:
            emoji = "🔴"
            color = 0xff0000
        
        message = f"""
{emoji} **{action} {symbol}**
💰 Cena: ${price:,.2f}
📊 Množství: {quantity:.6f}
📝 Důvod: {reason}
⏰ Čas: {datetime.now().strftime('%H:%M:%S')}
"""
        
        # Odeslat na všechny platformy
        self.send_discord(message, color)
        self.send_telegram(message)
        
        # Email jen pro důležité obchody
        if trade_info.get('confidence', 0) > 80:
            subject = f"🚨 HIGH CONFIDENCE {action}: {symbol}"
            self.send_email(subject, message)
    
    def notify_error(self, error_message: str):
        """Notifikace o chybě"""
        message = f"❌ **CHYBA TRADING BOTA**\n\n{error_message}\n\n⏰ {datetime.now()}"
        
        self.send_discord(message, 0xff0000)
        self.send_telegram(message)
        self.send_email("🚨 Trading Bot Error", message)
    
    def notify_daily_summary(self, summary: Dict):
        """Denní souhrn"""
        pnl = summary['total_pnl']
        trades = summary['total_trades']
        win_rate = summary['win_rate']
        balance = summary['balance']
        
        emoji = "📈" if pnl > 0 else "📉" if pnl < 0 else "➡️"
        
        message = f"""
📊 **DENNÍ SOUHRN**

{emoji} P&L: ${pnl:+,.2f}
🔄 Obchody: {trades}
🎯 Win Rate: {win_rate:.1f}%
💰 Balance: ${balance:,.2f}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M')}
"""
        
        self.send_discord(message)
        self.send_telegram(message)
        self.send_email("📊 Denní souhrn trading bota", message)

class PerformanceMonitor:
    """Monitor výkonu bota"""
    
    def __init__(self):
        self.metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0,
            'max_drawdown': 0,
            'current_drawdown': 0,
            'peak_balance': 0,
            'start_balance': 0
        }
    
    def update_metrics(self, trade_pnl: float, current_balance: float):
        """Aktualizace metrik"""
        self.metrics['total_trades'] += 1
        
        if trade_pnl > 0:
            self.metrics['winning_trades'] += 1
        
        self.metrics['total_pnl'] += trade_pnl
        
        # Drawdown tracking
        if current_balance > self.metrics['peak_balance']:
            self.metrics['peak_balance'] = current_balance
            self.metrics['current_drawdown'] = 0
        else:
            self.metrics['current_drawdown'] = (self.metrics['peak_balance'] - current_balance) / self.metrics['peak_balance']
            self.metrics['max_drawdown'] = max(self.metrics['max_drawdown'], self.metrics['current_drawdown'])
    
    def get_performance_report(self) -> Dict:
        """Generování reportu výkonu"""
        win_rate = (self.metrics['winning_trades'] / max(1, self.metrics['total_trades'])) * 100
        
        return {
            'total_trades': self.metrics['total_trades'],
            'win_rate': win_rate,
            'total_pnl': self.metrics['total_pnl'],
            'max_drawdown': self.metrics['max_drawdown'] * 100,
            'current_drawdown': self.metrics['current_drawdown'] * 100,
            'profit_factor': self.calculate_profit_factor()
        }
    
    def calculate_profit_factor(self) -> float:
        """Výpočet profit factor"""
        # Zjednodušená verze - v reálné implementaci by se počítalo z historie obchodů
        if self.metrics['total_pnl'] > 0:
            return 1.5  # Placeholder
        else:
            return 0.8
