#!/usr/bin/env python3
"""
🧠 TEST STARTUP ANALÝZY TRHU
Testování nové funkce analýzy před spuštěním obchodování
"""

import sys
from bot import TradingBot
from colors import print_banner, success, error, highlight

def test_startup_analysis():
    """Test startup analýzy"""
    print_banner("🧠 TEST STARTUP ANALÝZY TRHU")
    
    try:
        # Vytvoření bot instance
        bot = TradingBot()
        
        # Inicializace burzy
        if not bot.initialize_exchange():
            print(error("❌ Nepodařilo se připojit k burze"))
            return False
        
        print(f"\n{highlight('🧠 SPOUŠTÍM STARTUP ANALÝZU...')}")
        
        # Spuštění startup analýzy
        market_conditions, trading_mode = bot.startup_market_analysis()
        
        print(f"\n📊 {highlight('VÝSLEDKY ANALÝZY')}:")
        print(f"   🎯 Trading mód: {highlight(trading_mode.upper())}")
        print(f"   📈 Analyzováno symbolů: {len(market_conditions)}")
        
        # Detail pro každý symbol
        for symbol, conditions in market_conditions.items():
            print(f"\n📊 {symbol}:")
            print(f"   💰 Cena: {conditions['price']:.4f}")
            print(f"   📈 RSI: {conditions['rsi']:.2f}")
            print(f"   📊 Trend: {conditions['trend']}")
            print(f"   🎯 MACD: {conditions['momentum']}")
            print(f"   🧠 Strategie: {conditions['strategy']} ({conditions['confidence']:.0f}%)")
            print(f"   💡 Doporučení: {conditions['recommendation']}")
            print(f"   🎭 Sentiment: {conditions['sentiment']}")
        
        # Test adaptace trading módu
        print(f"\n🔧 {highlight('TEST ADAPTACE PODLE MÓDU')}:")
        
        original_oversold = bot.strategy.oversold
        original_overbought = bot.strategy.overbought
        
        print(f"   📊 Původní RSI: {original_oversold}/{original_overbought}")
        
        # Test konzervativního módu
        bot.trading_mode = "conservative"
        bot.strategy.oversold = max(40, bot.strategy.oversold)
        bot.strategy.overbought = min(60, bot.strategy.overbought)
        print(f"   🛡️ Konzervativní: {bot.strategy.oversold}/{bot.strategy.overbought}")
        
        # Test agresivního módu
        bot.trading_mode = "aggressive"
        bot.strategy.oversold = min(50, original_oversold)
        bot.strategy.overbought = max(50, original_overbought)
        print(f"   🚀 Agresivní: {bot.strategy.oversold}/{bot.strategy.overbought}")
        
        # Reset
        bot.strategy.oversold = original_oversold
        bot.strategy.overbought = original_overbought
        bot.trading_mode = trading_mode
        
        print(f"\n✅ {success('STARTUP ANALÝZA ÚSPĚŠNÁ!')}")
        print(f"   🎯 Doporučený mód: {highlight(trading_mode.upper())}")
        print(f"   📊 Bot je připraven k obchodování")
        
        return True
        
    except Exception as e:
        print(error(f"❌ Chyba při testu startup analýzy: {e}"))
        import traceback
        traceback.print_exc()
        return False

def show_startup_benefits():
    """Výhody startup analýzy"""
    print(f"\n🎯 {highlight('VÝHODY STARTUP ANALÝZY')}:")
    print("=" * 60)
    
    benefits = [
        "🧠 Inteligentní analýza trhu před spuštěním",
        "📊 Automatický výběr trading módu (aggressive/conservative/balanced)",
        "🎯 Adaptivní RSI parametry podle tržních podmínek",
        "💡 Jasné doporučení pro každý symbol",
        "📱 Discord notifikace o startup analýze",
        "🛡️ Ochrana před špatným timingem spuštění",
        "🚀 Optimalizace pro aktuální tržní sentiment",
        "📈 Lepší rozhodování od prvního cyklu"
    ]
    
    for benefit in benefits:
        print(f"   ✅ {benefit}")

def show_trading_modes():
    """Vysvětlení trading módů"""
    print(f"\n🎯 {highlight('TRADING MÓDY')}:")
    print("=" * 60)
    
    modes = {
        "🚀 AGGRESSIVE": {
            "kdy": "60%+ symbolů bullish",
            "rsi": "Nižší limity (více obchodů)",
            "chování": "Agresivní nákupy, rychlé reakce",
            "riziko": "Vyšší, ale vyšší zisky"
        },
        "🛡️ CONSERVATIVE": {
            "kdy": "60%+ symbolů bearish", 
            "rsi": "Vyšší limity (méně obchodů)",
            "chování": "Opatrné nákupy, čekání na jasné signály",
            "riziko": "Nižší, ochrana kapitálu"
        },
        "📊 BALANCED": {
            "kdy": "Smíšené tržní podmínky",
            "rsi": "Standardní limity (45/55)",
            "chování": "Vyvážený přístup",
            "riziko": "Střední, univerzální"
        }
    }
    
    for mode, info in modes.items():
        print(f"\n{mode}:")
        print(f"   📊 Kdy: {info['kdy']}")
        print(f"   📈 RSI: {info['rsi']}")
        print(f"   🎯 Chování: {info['chování']}")
        print(f"   ⚖️ Riziko: {info['riziko']}")

def main():
    """Hlavní funkce"""
    print("🧠 TEST STARTUP ANALÝZY TRADING BOTA")
    print("=" * 70)
    
    # Test startup analýzy
    if test_startup_analysis():
        print(f"\n🎉 {success('VŠECHNY TESTY PROŠLY!')}")
        
        show_startup_benefits()
        show_trading_modes()
        
        print(f"\n🚀 {highlight('SPUŠTĚNÍ S STARTUP ANALÝZOU')}:")
        print(f"python main.py --mode live")
        print(f"\n💡 Bot nyní:")
        print(f"   1. Provede analýzu trhu")
        print(f"   2. Vybere optimální trading mód")
        print(f"   3. Adaptuje parametry")
        print(f"   4. Začne inteligentně obchodovat")
        
    else:
        print(f"\n❌ {error('NĚKTERÉ TESTY SELHALY')}")
        print(f"Zkontrolujte chyby výše")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
