@echo off
chcp 65001 >nul
title Trading Bot - RSI Strategie

echo.
echo ============================================================
echo 🤖 TRADING BOT - RSI STRATEGIE
echo ============================================================
echo Automatický trading bot pro futures obchodování
echo Podporuje Binance a Bybit (demo i live účty)
echo ============================================================
echo.

REM Kontrola Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python není nainstalován nebo není v PATH!
    echo 📦 Nainstalujte Python z https://python.org
    pause
    exit /b 1
)

REM Kontrola .env souboru
if not exist .env (
    echo ❌ Soubor .env neexistuje!
    echo 📋 Zkopírujte .env.example jako .env a vyplňte své API klíče:
    echo    copy .env.example .env
    echo    # Poté upravte .env soubor
    pause
    exit /b 1
)

REM Spuštění Python skriptu
python run_bot.py

pause
