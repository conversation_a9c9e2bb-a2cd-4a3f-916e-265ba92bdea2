#!/usr/bin/env python3
"""
Porovnání starých a nových RSI úrovní
"""

from colors import print_banner, success, warning, highlight, profit, loss

def compare_strategies():
    """Porovnání strategií"""
    print_banner("📊 POROVNÁNÍ RSI STRATEGIÍ 📊")
    
    print(highlight("🔴 STARÁ STRATEGIE (konzervativní):"))
    print(f"   • RSI ≤ 30 = BUY (velmi vzácné)")
    print(f"   • RSI ≥ 70 = SELL (téměř nikdy)")
    print(f"   • RSI 30-70 = HOLD (95% času)")
    print(warning("   ⚠️  Výsledek: Jen 2 obchody za 90 dní"))
    
    print(f"\n{highlight('🟢 NOVÁ STRATEGIE (aktivnější):')}")
    print(f"   • RSI ≤ 35 = BUY (častější)")
    print(f"   • RSI ≥ 65 = SELL (ví<PERSON> signá<PERSON>)")
    print(f"   • RSI 35-65 = HOLD (men<PERSON><PERSON> neut<PERSON> zón<PERSON>)")
    print(success("   ✅ Očekáváme: 5-10 obchodů za 90 dní"))
    
    print(f"\n{highlight('📈 ANALÝZA LOGŮ - RSI HODNOTY:')}")
    
    # Simulace RSI hodnot z logů
    rsi_values = [30.08, 31.67, 58.56, 40.51, 56.59, 44.49, 67.31, 52.62, 75.38, 
                  50.89, 38.40, 49.38, 63.87, 61.90, 37.74, 48.54, 45.08, 49.48, 
                  35.08, 61.57, 50.87, 27.92, 33.35, 40.21, 47.80, 72.53]
    
    old_signals = 0
    new_signals = 0
    
    for rsi in rsi_values:
        # Stará strategie
        if rsi <= 30 or rsi >= 70:
            old_signals += 1
        
        # Nová strategie  
        if rsi <= 35 or rsi >= 65:
            new_signals += 1
    
    print(f"   📊 Vzorka 26 RSI hodnot z logů:")
    print(f"   🔴 Stará (30/70): {old_signals} signálů")
    print(f"   🟢 Nová (35/65): {new_signals} signálů")
    print(f"   📈 Nárůst: {((new_signals/old_signals)-1)*100:.0f}% více signálů!")
    
    print(f"\n{highlight('🎯 DOPORUČENÍ:')}")
    print(success("✅ Nová strategie 35/65 je vyvážená"))
    print(success("✅ Více obchodních příležitostí"))
    print(success("✅ Stále konzervativní risk management"))
    print(success("✅ Lepší využití kapitálu"))

if __name__ == "__main__":
    compare_strategies()
