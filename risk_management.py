from typing import Dict, Optional
from logger import logger

class RiskManager:
    """Správa rizik a pozic"""
    
    def __init__(self, max_position_size=0.25, stop_loss_pct=3.0, take_profit_pct=5.0):
        self.max_position_size = max_position_size  # ZVÝŠENO na 25% z kapitálu
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.open_positions = {}  # Sledování o<PERSON> pozic
    
    def calculate_position_size(self, balance: float, price: float, symbol: str) -> float:
        """Výpočet velikosti pozice"""
        try:
            # Maximální hodnota pozice v USDT
            max_position_value = balance * self.max_position_size
            
            # Velikost pozice v base currency
            position_size = max_position_value / price
            
            logger.debug(f"Position size calculation for {symbol}: "
                        f"Balance: {balance}, Price: {price}, "
                        f"Max value: {max_position_value}, Size: {position_size}")
            
            return position_size
            
        except Exception as e:
            logger.error(f"Chyba při výpočtu velikosti pozice pro {symbol}: {e}")
            return 0.0
    
    def validate_trade(self, symbol: str, side: str, quantity: float, 
                      price: float, balance: float) -> Dict:
        """Validace obchodu před provedením"""
        try:
            # Kontrola minimální velikosti obchodu
            trade_value = quantity * price
            if trade_value < 10:  # Minimální obchod 10 USDT
                return {
                    'valid': False,
                    'reason': f'Obchod příliš malý: {trade_value:.2f} USDT < 10 USDT'
                }
            
            # Kontrola dostupného kapitálu - AGRESIVNÍ (méně rezervy)
            if side == 'buy':
                if trade_value > balance * 0.98:  # Jen 2% rezerva pro agresivní trading
                    return {
                        'valid': False,
                        'reason': f'Nedostatek kapitálu: {trade_value:.2f} > {balance * 0.98:.2f}'
                    }
            
            # Kontrola maximální velikosti pozice - AGRESIVNÍ
            max_allowed = balance * self.max_position_size

            # Pro SELL obchody - inteligentní částečný prodej
            if side == 'sell':
                # Při prodeji kontrolujeme zda máme pozici
                if symbol not in self.open_positions:
                    return {
                        'valid': False,
                        'reason': f'Žádná pozice k prodeji pro {symbol}'
                    }

                # Získání informací o pozici
                position = self.open_positions[symbol]
                available_quantity = position['quantity']

                # Pokud chceme prodat více než máme, prodáme vše
                if quantity > available_quantity:
                    return {
                        'valid': True,
                        'adjusted': True,
                        'original_quantity': quantity,
                        'adjusted_quantity': available_quantity,
                        'reason': f'Prodej upraven na dostupné množství: {available_quantity:.6f}'
                    }

                # Pokud obchod překračuje limit, prodáme jen 50%
                if trade_value > max_allowed:
                    partial_quantity = quantity * 0.5
                    return {
                        'valid': True,
                        'adjusted': True,
                        'partial_sell': True,
                        'original_quantity': quantity,
                        'adjusted_quantity': partial_quantity,
                        'reason': f'Částečný prodej 50%: {trade_value:.2f} > {max_allowed:.2f}, prodáváme: {partial_quantity:.6f}'
                    }

                # Normální prodej
                return {
                    'valid': True,
                    'reason': 'Prodej existující pozice'
                }

            # Pro BUY obchody - inteligentní sizing
            if trade_value > max_allowed:
                # Výpočet maximální možné pozice
                max_quantity = max_allowed / price
                return {
                    'valid': True,
                    'adjusted': True,
                    'original_quantity': quantity,
                    'adjusted_quantity': max_quantity,
                    'reason': f'Pozice upravena: {trade_value:.2f} > {max_allowed:.2f}, nová velikost: {max_quantity:.6f}'
                }
            
            # Kontrola duplicitních pozic
            if side == 'buy' and symbol in self.open_positions:
                return {
                    'valid': False,
                    'reason': f'Pozice pro {symbol} již existuje'
                }
            
            return {
                'valid': True,
                'reason': 'Obchod validní'
            }
            
        except Exception as e:
            logger.error(f"Chyba při validaci obchodu: {e}")
            return {
                'valid': False,
                'reason': f'Chyba validace: {str(e)}'
            }
    
    def calculate_stop_loss_take_profit(self, entry_price: float, side: str) -> Dict:
        """Výpočet Stop Loss a Take Profit cen"""
        try:
            if side == 'buy' or side == 'long':
                stop_loss = entry_price * (1 - self.stop_loss_pct / 100)
                take_profit = entry_price * (1 + self.take_profit_pct / 100)
            else:  # sell nebo short
                stop_loss = entry_price * (1 + self.stop_loss_pct / 100)
                take_profit = entry_price * (1 - self.take_profit_pct / 100)
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit
            }
            
        except Exception as e:
            logger.error(f"Chyba při výpočtu SL/TP: {e}")
            return {
                'stop_loss': entry_price,
                'take_profit': entry_price
            }
    
    def add_position(self, symbol: str, side: str, quantity: float, 
                    entry_price: float, stop_loss: float, take_profit: float):
        """Přidání pozice do sledování"""
        self.open_positions[symbol] = {
            'side': side,
            'quantity': quantity,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'unrealized_pnl': 0.0
        }
        
        logger.info(f"Pozice přidána: {symbol} {side} {quantity} @ {entry_price}")
    
    def remove_position(self, symbol: str):
        """Odebrání pozice ze sledování"""
        if symbol in self.open_positions:
            del self.open_positions[symbol]
            logger.info(f"Pozice odebrána: {symbol}")
    
    def update_unrealized_pnl(self, symbol: str, current_price: float):
        """Aktualizace nerealizovaného P&L"""
        if symbol not in self.open_positions:
            return
        
        position = self.open_positions[symbol]
        entry_price = position['entry_price']
        quantity = position['quantity']
        
        if position['side'] in ['buy', 'long']:
            pnl = (current_price - entry_price) * quantity
        else:
            pnl = (entry_price - current_price) * quantity
        
        position['unrealized_pnl'] = pnl
    
    def check_exit_conditions(self, symbol: str, current_price: float) -> Dict:
        """Kontrola podmínek pro uzavření pozice"""
        if symbol not in self.open_positions:
            return {'should_exit': False, 'reason': ''}
        
        position = self.open_positions[symbol]
        stop_loss = position['stop_loss']
        take_profit = position['take_profit']
        
        if position['side'] in ['buy', 'long']:
            if current_price <= stop_loss:
                return {
                    'should_exit': True,
                    'reason': f'Stop Loss: {current_price} <= {stop_loss}'
                }
            elif current_price >= take_profit:
                return {
                    'should_exit': True,
                    'reason': f'Take Profit: {current_price} >= {take_profit}'
                }
        else:  # short pozice
            if current_price >= stop_loss:
                return {
                    'should_exit': True,
                    'reason': f'Stop Loss: {current_price} >= {stop_loss}'
                }
            elif current_price <= take_profit:
                return {
                    'should_exit': True,
                    'reason': f'Take Profit: {current_price} <= {take_profit}'
                }
        
        return {'should_exit': False, 'reason': ''}
    
    def get_portfolio_summary(self) -> Dict:
        """Souhrn portfolia"""
        total_positions = len(self.open_positions)
        total_unrealized_pnl = sum(pos['unrealized_pnl'] for pos in self.open_positions.values())
        
        return {
            'total_positions': total_positions,
            'total_unrealized_pnl': total_unrealized_pnl,
            'positions': dict(self.open_positions)
        }
