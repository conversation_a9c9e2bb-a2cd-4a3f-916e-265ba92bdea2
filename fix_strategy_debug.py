#!/usr/bin/env python3
"""
Debug a oprava strategie pro reálná data
"""

from colors import print_banner, success, error, warning, highlight

def analyze_problem():
    """Analýza problému s botem"""
    print_banner("🔍 ANALÝZA PROBLÉMU S BOTEM 🔍")
    
    print(error("❌ IDENTIFIKOVANÉ PROBLÉMY:"))
    print("1. 🤖 Bot používá DEMO data místo reálných")
    print("2. 📊 Stará strategie (30/70) místo nové (35/65)")
    print("3. 🔌 Žádné skutečné API připojení")
    print("4. 📈 Graf ukazuje perfektní příležitosti, ale bot je nevidí")
    
    print(f"\n{highlight('📊 Z GRAFU VIDÍM:')}")
    print(success("✅ BTC/USDT osciluje kolem 107,000"))
    print(success("✅ Krásný uptrend s MA podporou"))
    print(success("✅ Perfektní nákupní dips"))
    print(success("✅ Volatilita ideální pro RSI trading"))
    
    print(f"\n{warning('⚠️ PROČ BOT NEOBCHODUJE:')}")
    print("1. 🔴 API klíče nejsou správně nastavené")
    print("2. 🔴 Bot běží v demo módu s fake daty")
    print("3. 🔴 Strategie je příliš konzervativní")
    print("4. 🔴 Chybí připojení k reálným tržním datům")
    
    print(f"\n{highlight('🛠️ ŘEŠENÍ:')}")
    print(success("1. ✅ Nastavit správné API klíče"))
    print(success("2. ✅ Spustit live mode s reálnými daty"))
    print(success("3. ✅ Použít agresivnější strategii (40/60)"))
    print(success("4. ✅ Zkrátit interval na 30 sekund"))
    
    print(f"\n{highlight('🎯 DOPORUČENÁ STRATEGIE PRO TENTO TRH:')}")
    print("📊 RSI Oversold: 40 (místo 35)")
    print("📊 RSI Overbought: 60 (místo 65)")
    print("⏰ Interval: 30 sekund (místo 60)")
    print("📈 Timeframe: 5m (místo 1h)")
    print("💰 Position size: 15% (místo 20%)")
    
    print(f"\n{error('🚨 KRITICKÉ:')}")
    print("Bot MUSÍ být připojen k reálné burze!")
    print("Demo mode NIKDY neuvidí skutečné tržní pohyby!")

def show_quick_fix():
    """Rychlá oprava"""
    print(f"\n{print_banner('⚡ RYCHLÁ OPRAVA ⚡')}")
    
    print(highlight("1. 🔑 NASTAVTE API KLÍČE:"))
    print("   • Jděte na Binance testnet")
    print("   • Vygenerujte nové API klíče")
    print("   • Zkopírujte do .env souboru")
    
    print(f"\n{highlight('2. 🎯 AGRESIVNĚJŠÍ STRATEGIE:')}")
    print("   • RSI 40/60 pro více signálů")
    print("   • 5m timeframe pro rychlejší reakce")
    print("   • 30s interval pro častější kontroly")
    
    print(f"\n{highlight('3. 🚀 SPUŠTĚNÍ:')}")
    print(success("python main.py --mode live"))
    print(warning("POZOR: Ujistěte se, že máte správné API klíče!"))

if __name__ == "__main__":
    analyze_problem()
    show_quick_fix()
