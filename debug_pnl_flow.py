#!/usr/bin/env python3
"""
🔍 DEBUG P&L FLOW
Detailní analýza toku P&L výpočtu pro identifikaci problému
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from colors import print_banner, success, error, highlight, warning

def analyze_pnl_flow():
    """Analýza toku P&L výpočtu"""
    print_banner("🔍 DEBUG P&L FLOW ANALÝZA")
    
    print(f"\n📊 {highlight('ANALÝZA TOKU P&L VÝPOČTU')}:")
    print("=" * 70)
    
    flow_steps = [
        {
            'step': 1,
            'action': 'BUY obchod',
            'code': 'self.place_market_order(symbol, "buy", quantity)',
            'risk_manager': 'Pozice se PŘIDÁ do risk_manager.open_positions',
            'strategy': 'Pozice se PŘIDÁ do strategy.positions',
            'pnl': 'Žádný P&L výpočet (nákup)'
        },
        {
            'step': 2,
            'action': 'SELL obchod',
            'code': 'self.place_market_order(symbol, "sell", quantity)',
            'risk_manager': 'Pozice se HLEDÁ v risk_manager.open_positions',
            'strategy': 'Pozice se HLEDÁ ve strategy.positions',
            'pnl': 'P&L = (exit_price - entry_price) * quantity'
        },
        {
            'step': 3,
            'action': 'P&L výpočet',
            'code': 'if symbol in self.risk_manager.open_positions:',
            'risk_manager': 'KRITICKÉ: Pozice MUSÍ existovat!',
            'strategy': 'Fallback pokud risk_manager selže',
            'pnl': 'Pokud pozice neexistuje → P&L = None'
        },
        {
            'step': 4,
            'action': 'Odebrání pozice',
            'code': 'self.risk_manager.remove_position(symbol)',
            'risk_manager': 'Pozice se ODSTRANÍ',
            'strategy': 'Pozice se ODSTRANÍ',
            'pnl': 'P&L už je vypočítáno'
        }
    ]
    
    for step_info in flow_steps:
        print(f"\n🔢 {highlight(f'KROK {step_info["step"]}: {step_info["action"]}')}:")
        print(f"   💻 Kód: {step_info['code']}")
        print(f"   🎯 Risk Manager: {step_info['risk_manager']}")
        print(f"   📊 Strategy: {step_info['strategy']}")
        print(f"   💰 P&L: {step_info['pnl']}")

def identify_potential_problems():
    """Identifikace možných problémů"""
    print(f"\n🚨 {error('MOŽNÉ PŘÍČINY PROBLÉMU')}:")
    print("=" * 70)
    
    problems = [
        {
            'problem': 'Pozice se nepřidá do risk_manageru',
            'cause': 'Chyba při BUY obchodu',
            'symptom': 'risk_manager.open_positions je prázdný',
            'debug': 'Zkontrolovat add_position() volání'
        },
        {
            'problem': 'Pozice se přidá s jiným symbolem',
            'cause': 'Symbol mismatch (ADA/USDT vs ADAUSDT)',
            'symptom': 'Pozice existuje, ale pod jiným klíčem',
            'debug': 'Zkontrolovat symbol normalizaci'
        },
        {
            'problem': 'Pozice se odstraní předčasně',
            'cause': 'Jiný proces odstraní pozici',
            'symptom': 'Pozice zmizí mezi BUY a SELL',
            'debug': 'Zkontrolovat všechna remove_position() volání'
        },
        {
            'problem': 'Strategy pozice má špatný entry_price',
            'cause': 'Chyba v strategy.update_position()',
            'symptom': 'Fallback P&L je také 0',
            'debug': 'Zkontrolovat strategy pozice'
        },
        {
            'problem': 'Quantity mismatch',
            'cause': 'Jiné quantity při BUY vs SELL',
            'symptom': 'P&L se počítá s jiným množstvím',
            'debug': 'Zkontrolovat quantity hodnoty'
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\n❌ {error(f'PROBLÉM {i}: {problem["problem"]}')}:")
        print(f"   🔍 Příčina: {problem['cause']}")
        print(f"   📊 Symptom: {problem['symptom']}")
        print(f"   🔧 Debug: {problem['debug']}")

def create_debug_solution():
    """Vytvoření debug řešení"""
    print(f"\n🔧 {highlight('NAVRHOVANÉ DEBUG ŘEŠENÍ')}:")
    print("=" * 70)
    
    print(f"📊 {highlight('PŘIDÁNÍ DETAILNÍHO DEBUGGINGU')}:")
    
    debug_code = '''
# 🔍 DETAILNÍ DEBUG P&L VÝPOČTU
def place_market_order(self, symbol, side, quantity):
    try:
        # ... existující kód ...
        
        # 🔍 DEBUG: Stav PŘED P&L výpočtem
        print(f"🔍 DEBUG PŘED P&L: side={side}, symbol={symbol}")
        print(f"🔍 Risk Manager pozice: {list(self.risk_manager.open_positions.keys())}")
        print(f"🔍 Strategy pozice: {list(self.strategy.positions.keys())}")
        
        pnl = None
        if side == 'sell':
            # 🔍 DEBUG: Kontrola existence pozice
            if symbol in self.risk_manager.open_positions:
                rm_position = self.risk_manager.open_positions[symbol]
                entry_price = rm_position['entry_price']
                print(f"🔍 RISK MANAGER: entry={entry_price}, exit={fill_price}, qty={quantity}")
                pnl = (fill_price - entry_price) * quantity
                print(f"🔍 RISK MANAGER P&L: {pnl:.2f}")
            else:
                print(f"🔍 RISK MANAGER: Pozice {symbol} NEEXISTUJE!")
                # Fallback na strategy
                position = self.strategy.get_position(symbol)
                if position['quantity'] > 0:
                    entry_price = position['entry_price']
                    print(f"🔍 STRATEGY: entry={entry_price}, exit={fill_price}, qty={quantity}")
                    pnl = (fill_price - entry_price) * quantity
                    print(f"🔍 STRATEGY P&L: {pnl:.2f}")
                else:
                    print(f"🔍 STRATEGY: Pozice {symbol} má quantity=0!")
        
        print(f"🔍 FINÁLNÍ P&L: {pnl}")
        
        # ... zbytek kódu ...
    '''
    
    print(debug_code)

def show_testing_plan():
    """Plán testování"""
    print(f"\n🧪 {highlight('PLÁN TESTOVÁNÍ')}:")
    print("=" * 70)
    
    tests = [
        {
            'test': 'Test 1: Kontrola pozic po BUY',
            'action': 'Spustit bot, provést BUY, zkontrolovat pozice',
            'check': 'risk_manager.open_positions obsahuje pozici'
        },
        {
            'test': 'Test 2: Kontrola symbolů',
            'action': 'Ověřit že symbol je stejný v BUY i SELL',
            'check': 'Symbol normalizace funguje správně'
        },
        {
            'test': 'Test 3: Kontrola entry_price',
            'action': 'Ověřit že entry_price se ukládá správně',
            'check': 'entry_price není 0 nebo None'
        },
        {
            'test': 'Test 4: Kontrola quantity',
            'action': 'Ověřit že quantity je stejné v BUY i SELL',
            'check': 'Quantity mismatch'
        },
        {
            'test': 'Test 5: Manual P&L test',
            'action': 'Ruční výpočet P&L a porovnání',
            'check': 'Matematika je správná'
        }
    ]
    
    for test in tests:
        print(f"\n🧪 {test['test']}:")
        print(f"   🎯 Akce: {test['action']}")
        print(f"   ✅ Kontrola: {test['check']}")

def show_immediate_fix():
    """Okamžitá oprava"""
    print(f"\n⚡ {highlight('OKAMŽITÁ OPRAVA - PŘIDÁNÍ DEBUG VÝPISŮ')}:")
    print("=" * 70)
    
    print(f"🔧 {highlight('CO UDĚLAT HNED')}:")
    print(f"   1. Přidat detailní debug výpisy do place_market_order")
    print(f"   2. Spustit bot a sledovat debug výstupy")
    print(f"   3. Identifikovat kde se P&L ztrácí")
    print(f"   4. Opravit konkrétní problém")
    
    print(f"\n📊 {highlight('OČEKÁVANÉ DEBUG VÝSTUPY')}:")
    print(f"```")
    print(f"🔍 DEBUG PŘED P&L: side=sell, symbol=ADA/USDT")
    print(f"🔍 Risk Manager pozice: ['ADA/USDT']")
    print(f"🔍 Strategy pozice: ['ADA/USDT']")
    print(f"🔍 RISK MANAGER: entry=0.6169, exit=0.6200, qty=5255.02")
    print(f"🔍 RISK MANAGER P&L: 16.29")
    print(f"🔍 FINÁLNÍ P&L: 16.29")
    print(f"```")
    
    print(f"\n❌ {error('POKUD UVIDÍTE')}:")
    print(f"```")
    print(f"🔍 RISK MANAGER: Pozice ADA/USDT NEEXISTUJE!")
    print(f"🔍 STRATEGY: Pozice ADA/USDT má quantity=0!")
    print(f"🔍 FINÁLNÍ P&L: None")
    print(f"```")
    print(f"   → Pak víme kde je problém!")

def main():
    """Hlavní funkce"""
    print("🔍 DEBUG P&L FLOW - DETAILNÍ ANALÝZA")
    print("=" * 70)
    
    analyze_pnl_flow()
    identify_potential_problems()
    create_debug_solution()
    show_testing_plan()
    show_immediate_fix()
    
    print(f"\n🎯 {success('ZÁVĚR')}:")
    print(f"   🔍 Problém je někde v toku P&L výpočtu")
    print(f"   🔧 Potřebujeme detailní debug výpisy")
    print(f"   📊 Identifikovat kde se pozice/P&L ztrácí")
    print(f"   ⚡ Pak můžeme problém rychle opravit")
    
    print(f"\n🚀 {highlight('DALŠÍ KROK')}:")
    print(f"   Přidat debug výpisy do bot.py a spustit test!")

if __name__ == "__main__":
    main()
