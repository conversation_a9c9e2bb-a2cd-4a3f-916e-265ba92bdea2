import pandas as pd
import numpy as np
import ccxt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from config import Config
from strategy import RSIStrategy
from risk_management import RiskManager
from logger import logger

class Backtester:
    """Backtesting engine pro testování strategií"""
    
    def __init__(self, initial_balance=10000):
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.strategy = RSIStrategy(
            rsi_period=Config.RSI_PERIOD,
            oversold=Config.RSI_OVERSOLD,
            overbought=Config.RSI_OVERBOUGHT
        )
        self.risk_manager = RiskManager(
            max_position_size=Config.MAX_POSITION_SIZE,
            stop_loss_pct=Config.STOP_LOSS_PERCENT,
            take_profit_pct=Config.TAKE_PROFIT_PERCENT
        )
        self.trades = []
        self.equity_curve = []
        self.positions = {}
        
    def fetch_historical_data(self, symbol: str, timeframe: str, 
                            start_date: str, end_date: str) -> pd.DataFrame:
        """Získání historických dat"""
        try:
            # Inicializace burzy pro získání dat
            exchange = ccxt.binance()
            
            # Převod dat na timestamp
            start_ts = int(pd.Timestamp(start_date).timestamp() * 1000)
            end_ts = int(pd.Timestamp(end_date).timestamp() * 1000)
            
            all_data = []
            current_ts = start_ts
            
            logger.info(f"Stahování dat pro {symbol} od {start_date} do {end_date}")
            
            while current_ts < end_ts:
                try:
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=current_ts, limit=1000)
                    if not ohlcv:
                        break
                    
                    all_data.extend(ohlcv)
                    current_ts = ohlcv[-1][0] + 1
                    
                    # Krátká pauza aby nedošlo k rate limitu
                    import time
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"Chyba při stahování dat: {e}")
                    break
            
            if not all_data:
                logger.error(f"Nepodařilo se získat data pro {symbol}")
                return pd.DataFrame()
            
            # Převod na DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            df = df.drop_duplicates()
            
            logger.info(f"Získáno {len(df)} záznamů pro {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"Chyba při získávání historických dat: {e}")
            return pd.DataFrame()
    
    def simulate_trade(self, symbol: str, side: str, price: float, 
                      timestamp: pd.Timestamp, quantity: float = None) -> bool:
        """Simulace obchodu"""
        try:
            if quantity is None:
                quantity = self.risk_manager.calculate_position_size(
                    self.balance, price, symbol
                )
            
            # Validace obchodu
            validation = self.risk_manager.validate_trade(
                symbol, side, quantity, price, self.balance
            )
            
            if not validation['valid']:
                return False
            
            # Provedení obchodu
            trade_value = quantity * price
            
            if side == 'buy':
                self.balance -= trade_value
                self.positions[symbol] = {
                    'quantity': quantity,
                    'entry_price': price,
                    'entry_time': timestamp,
                    'side': 'long'
                }
                
                # Výpočet SL/TP
                sl_tp = self.risk_manager.calculate_stop_loss_take_profit(price, side)
                self.risk_manager.add_position(
                    symbol, side, quantity, price,
                    sl_tp['stop_loss'], sl_tp['take_profit']
                )
                
            elif side == 'sell' and symbol in self.positions:
                position = self.positions[symbol]
                self.balance += trade_value
                
                # Výpočet P&L
                pnl = (price - position['entry_price']) * position['quantity']
                
                # Záznam obchodu
                self.trades.append({
                    'symbol': symbol,
                    'entry_time': position['entry_time'],
                    'exit_time': timestamp,
                    'entry_price': position['entry_price'],
                    'exit_price': price,
                    'quantity': position['quantity'],
                    'pnl': pnl,
                    'return_pct': (pnl / (position['entry_price'] * position['quantity'])) * 100
                })
                
                # Odebrání pozice
                del self.positions[symbol]
                self.risk_manager.remove_position(symbol)
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba při simulaci obchodu: {e}")
            return False
    
    def run_backtest(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """Spuštění backtestingu"""
        logger.info(f"=== Spouštění backtestingu pro {symbol} ===")
        
        # Získání dat
        data = self.fetch_historical_data(symbol, Config.TIMEFRAME, start_date, end_date)
        if data.empty:
            return {'error': 'Nepodařilo se získat data'}
        
        # Reset stavu
        self.balance = self.initial_balance
        self.trades = []
        self.equity_curve = []
        self.positions = {}
        
        # Simulace obchodování
        for i in range(Config.RSI_PERIOD, len(data)):
            current_data = data.iloc[:i+1]
            current_price = current_data['close'].iloc[-1]
            current_time = current_data.index[-1]
            
            # Aktualizace nerealizovaného P&L
            if symbol in self.positions:
                self.risk_manager.update_unrealized_pnl(symbol, current_price)
            
            # Kontrola exit podmínek
            exit_check = self.risk_manager.check_exit_conditions(symbol, current_price)
            if exit_check['should_exit'] and symbol in self.positions:
                position = self.positions[symbol]
                self.simulate_trade(symbol, 'sell', current_price, current_time, position['quantity'])
                continue
            
            # Analýza signálu
            signal = self.strategy.analyze_signal(symbol, current_data)
            
            if signal['action'] == 'BUY' and symbol not in self.positions:
                self.simulate_trade(symbol, 'buy', current_price, current_time)
            elif signal['action'] == 'SELL' and symbol in self.positions:
                position = self.positions[symbol]
                self.simulate_trade(symbol, 'sell', current_price, current_time, position['quantity'])
            
            # Záznam equity curve
            total_value = self.balance
            if symbol in self.positions:
                position = self.positions[symbol]
                unrealized_pnl = (current_price - position['entry_price']) * position['quantity']
                total_value += position['entry_price'] * position['quantity'] + unrealized_pnl
            
            self.equity_curve.append({
                'timestamp': current_time,
                'balance': self.balance,
                'total_value': total_value,
                'price': current_price
            })
        
        # Uzavření otevřených pozic
        if symbol in self.positions:
            final_price = data['close'].iloc[-1]
            final_time = data.index[-1]
            position = self.positions[symbol]
            self.simulate_trade(symbol, 'sell', final_price, final_time, position['quantity'])
        
        return self.calculate_metrics()
    
    def calculate_metrics(self) -> Dict:
        """Výpočet metrik výkonnosti"""
        if not self.trades:
            return {'error': 'Žádné obchody nebyly provedeny'}
        
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.equity_curve)
        
        # Základní metriky
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        total_pnl = trades_df['pnl'].sum()
        total_return = (self.balance - self.initial_balance) / self.initial_balance * 100
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 else float('inf')
        
        # Sharpe ratio (zjednodušený)
        if len(equity_df) > 1:
            returns = equity_df['total_value'].pct_change().dropna()
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() != 0 else 0
        else:
            sharpe_ratio = 0
        
        # Maximum drawdown
        equity_df['peak'] = equity_df['total_value'].cummax()
        equity_df['drawdown'] = (equity_df['total_value'] - equity_df['peak']) / equity_df['peak'] * 100
        max_drawdown = equity_df['drawdown'].min()
        
        metrics = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_balance': self.balance,
            'trades': self.trades,
            'equity_curve': self.equity_curve
        }
        
        return metrics
