#!/usr/bin/env python3
"""
INTELIGENTNÍ VÝBĚR OBCHODNÍ STRATEGIE
Automatický výběr strategie podle aktuálního trendu a volatility
Implementace 8 nejlepších strategií z FXstreet.cz
"""

import ccxt
import pandas as pd
import ta
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class IntelligentStrategySelector:
    """Inteligentní výběr obchodní strategie podle tržních podmínek"""
    
    def __init__(self):
        self.strategies = {
            'TREND_FOLLOWING': {
                'name': 'Trend Following',
                'description': 'Následování silného trendu',
                'conditions': ['strong_trend', 'high_momentum'],
                'timeframe': '1h',
                'risk_level': 'medium'
            },
            'RANGE_TRADING': {
                'name': 'Range Trading', 
                'description': 'Obchodování v pásmu (sideways)',
                'conditions': ['sideways', 'low_volatility'],
                'timeframe': '15m',
                'risk_level': 'low'
            },
            'SWING_TRADING': {
                'name': 'Swing Trading',
                'description': 'Středněddobé pozice na korekce',
                'conditions': ['medium_trend', 'medium_volatility'],
                'timeframe': '4h',
                'risk_level': 'medium'
            },
            'SCALPING': {
                'name': 'Scalping',
                'description': 'Velmi rychlé obchody',
                'conditions': ['high_volatility', 'any_trend'],
                'timeframe': '1m',
                'risk_level': 'high'
            },
            'DAY_TRADING': {
                'name': 'Day Trading',
                'description': 'Intradenní obchodování',
                'conditions': ['medium_volatility', 'clear_signals'],
                'timeframe': '5m',
                'risk_level': 'medium'
            },
            'PRICE_ACTION': {
                'name': 'Price Action',
                'description': 'Čtení cenových formací',
                'conditions': ['clear_patterns', 'any_volatility'],
                'timeframe': '15m',
                'risk_level': 'medium'
            }
        }
        
        self.current_strategy = None
        self.market_analysis = {}
        
    def analyze_market_conditions(self, exchange, symbol: str) -> Dict:
        """Analýza tržních podmínek pro výběr strategie"""
        try:
            # Získání dat pro různé timeframy
            timeframes = ['1m', '5m', '15m', '1h', '4h']
            market_data = {}
            
            for tf in timeframes:
                ohlcv = exchange.fetch_ohlcv(symbol, tf, limit=100)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                market_data[tf] = df
            
            # Analýza trendu
            trend_analysis = self._analyze_trend(market_data['1h'])
            
            # Analýza volatility
            volatility_analysis = self._analyze_volatility(market_data['5m'])
            
            # Analýza momentum
            momentum_analysis = self._analyze_momentum(market_data['15m'])
            
            # Analýza support/resistance
            sr_analysis = self._analyze_support_resistance(market_data['15m'])
            
            # Analýza price action patterns
            pattern_analysis = self._analyze_patterns(market_data['5m'])
            
            return {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'trend': trend_analysis,
                'volatility': volatility_analysis,
                'momentum': momentum_analysis,
                'support_resistance': sr_analysis,
                'patterns': pattern_analysis,
                'market_type': self._determine_market_type(trend_analysis, volatility_analysis)
            }
            
        except Exception as e:
            print(f"❌ Chyba analýzy trhu: {e}")
            return None
    
    def _analyze_trend(self, df: pd.DataFrame) -> Dict:
        """Analýza trendu"""
        # Moving averages
        df['ma20'] = df['close'].rolling(window=20).mean()
        df['ma50'] = df['close'].rolling(window=50).mean()
        df['ma200'] = df['close'].rolling(window=200).mean() if len(df) >= 200 else df['close'].rolling(window=len(df)//2).mean()
        
        current_price = df['close'].iloc[-1]
        ma20 = df['ma20'].iloc[-1]
        ma50 = df['ma50'].iloc[-1]
        ma200 = df['ma200'].iloc[-1]
        
        # Trend síla
        if current_price > ma20 > ma50 > ma200:
            trend_strength = 'very_strong_uptrend'
            trend_score = 5
        elif current_price > ma20 > ma50:
            trend_strength = 'strong_uptrend'
            trend_score = 4
        elif current_price > ma20:
            trend_strength = 'weak_uptrend'
            trend_score = 3
        elif current_price < ma20 < ma50 < ma200:
            trend_strength = 'very_strong_downtrend'
            trend_score = -5
        elif current_price < ma20 < ma50:
            trend_strength = 'strong_downtrend'
            trend_score = -4
        elif current_price < ma20:
            trend_strength = 'weak_downtrend'
            trend_score = -3
        else:
            trend_strength = 'sideways'
            trend_score = 0
        
        # ADX pro sílu trendu
        adx_indicator = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        adx = adx_indicator.adx().iloc[-1] if not adx_indicator.adx().isna().all() else 25
        
        return {
            'strength': trend_strength,
            'score': trend_score,
            'adx': adx,
            'direction': 'up' if trend_score > 0 else 'down' if trend_score < 0 else 'sideways',
            'ma_alignment': current_price > ma20 > ma50
        }
    
    def _analyze_volatility(self, df: pd.DataFrame) -> Dict:
        """Analýza volatility"""
        # ATR
        atr_indicator = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close'])
        atr = atr_indicator.average_true_range().iloc[-1]
        
        # Bollinger Bands
        bb_indicator = ta.volatility.BollingerBands(close=df['close'])
        bb_width = (bb_indicator.bollinger_hband().iloc[-1] - bb_indicator.bollinger_lband().iloc[-1]) / df['close'].iloc[-1] * 100
        
        # Volatilita klasifikace
        if bb_width > 4:
            volatility_level = 'very_high'
            vol_score = 5
        elif bb_width > 2.5:
            volatility_level = 'high'
            vol_score = 4
        elif bb_width > 1.5:
            volatility_level = 'medium'
            vol_score = 3
        elif bb_width > 1:
            volatility_level = 'low'
            vol_score = 2
        else:
            volatility_level = 'very_low'
            vol_score = 1
        
        return {
            'level': volatility_level,
            'score': vol_score,
            'atr': atr,
            'bb_width': bb_width,
            'suitable_for_scalping': vol_score >= 4,
            'suitable_for_range': vol_score <= 2
        }
    
    def _analyze_momentum(self, df: pd.DataFrame) -> Dict:
        """Analýza momentum"""
        # RSI
        rsi = ta.momentum.RSIIndicator(close=df['close']).rsi().iloc[-1]
        
        # MACD
        macd_indicator = ta.trend.MACD(close=df['close'])
        macd = macd_indicator.macd().iloc[-1]
        macd_signal = macd_indicator.macd_signal().iloc[-1]
        macd_histogram = macd_indicator.macd_diff().iloc[-1]
        
        # Stochastic
        stoch_indicator = ta.momentum.StochasticOscillator(high=df['high'], low=df['low'], close=df['close'])
        stoch_k = stoch_indicator.stoch().iloc[-1]
        
        # Momentum síla
        momentum_signals = 0
        if macd > macd_signal:
            momentum_signals += 1
        if rsi > 50:
            momentum_signals += 1
        if stoch_k > 50:
            momentum_signals += 1
        
        momentum_strength = 'strong' if momentum_signals >= 2 else 'weak'
        
        return {
            'rsi': rsi,
            'macd': macd,
            'macd_signal': macd_signal,
            'macd_histogram': macd_histogram,
            'stoch_k': stoch_k,
            'strength': momentum_strength,
            'bullish': momentum_signals >= 2,
            'oversold': rsi < 30,
            'overbought': rsi > 70
        }
    
    def _analyze_support_resistance(self, df: pd.DataFrame) -> Dict:
        """Analýza support/resistance úrovní"""
        current_price = df['close'].iloc[-1]
        
        # Pivot points
        high = df['high'].iloc[-1]
        low = df['low'].iloc[-1]
        close = df['close'].iloc[-2]  # Předchozí close
        
        pivot = (high + low + close) / 3
        r1 = 2 * pivot - low
        s1 = 2 * pivot - high
        r2 = pivot + (high - low)
        s2 = pivot - (high - low)
        
        # Vzdálenost od klíčových úrovní
        distance_to_resistance = min(abs(current_price - r1), abs(current_price - r2))
        distance_to_support = min(abs(current_price - s1), abs(current_price - s2))
        
        return {
            'pivot': pivot,
            'resistance_1': r1,
            'resistance_2': r2,
            'support_1': s1,
            'support_2': s2,
            'near_resistance': distance_to_resistance < current_price * 0.005,
            'near_support': distance_to_support < current_price * 0.005,
            'in_range': s1 < current_price < r1
        }
    
    def _analyze_patterns(self, df: pd.DataFrame) -> Dict:
        """Analýza price action patterns"""
        # Doji pattern
        last_candle = df.iloc[-1]
        body_size = abs(last_candle['close'] - last_candle['open'])
        candle_range = last_candle['high'] - last_candle['low']
        
        is_doji = body_size < candle_range * 0.1
        
        # Hammer/Shooting star
        upper_shadow = last_candle['high'] - max(last_candle['open'], last_candle['close'])
        lower_shadow = min(last_candle['open'], last_candle['close']) - last_candle['low']
        
        is_hammer = lower_shadow > body_size * 2 and upper_shadow < body_size
        is_shooting_star = upper_shadow > body_size * 2 and lower_shadow < body_size
        
        # Engulfing pattern
        prev_candle = df.iloc[-2]
        is_bullish_engulfing = (prev_candle['close'] < prev_candle['open'] and 
                               last_candle['close'] > last_candle['open'] and
                               last_candle['close'] > prev_candle['open'] and
                               last_candle['open'] < prev_candle['close'])
        
        is_bearish_engulfing = (prev_candle['close'] > prev_candle['open'] and 
                               last_candle['close'] < last_candle['open'] and
                               last_candle['close'] < prev_candle['open'] and
                               last_candle['open'] > prev_candle['close'])
        
        return {
            'doji': is_doji,
            'hammer': is_hammer,
            'shooting_star': is_shooting_star,
            'bullish_engulfing': is_bullish_engulfing,
            'bearish_engulfing': is_bearish_engulfing,
            'clear_patterns': any([is_doji, is_hammer, is_shooting_star, is_bullish_engulfing, is_bearish_engulfing])
        }
    
    def _determine_market_type(self, trend: Dict, volatility: Dict) -> str:
        """Určení typu trhu"""
        trend_score = abs(trend['score'])
        vol_score = volatility['score']
        
        if trend_score >= 4:
            return 'strong_trending'
        elif trend_score >= 2:
            return 'weak_trending'
        elif vol_score >= 4:
            return 'high_volatility_ranging'
        elif vol_score <= 2:
            return 'low_volatility_ranging'
        else:
            return 'mixed_conditions'
    
    def select_optimal_strategy(self, market_analysis: Dict) -> Dict:
        """Výběr optimální strategie podle tržních podmínek"""
        if not market_analysis:
            return None
        
        trend = market_analysis['trend']
        volatility = market_analysis['volatility']
        momentum = market_analysis['momentum']
        sr = market_analysis['support_resistance']
        patterns = market_analysis['patterns']
        market_type = market_analysis['market_type']
        
        strategy_scores = {}
        
        # Hodnocení strategií podle podmínek
        
        # TREND FOLLOWING - nejlepší pro silné trendy
        if market_type == 'strong_trending' and abs(trend['score']) >= 4:
            strategy_scores['TREND_FOLLOWING'] = 90 + trend['adx']
        elif abs(trend['score']) >= 2:
            strategy_scores['TREND_FOLLOWING'] = 60 + abs(trend['score']) * 10
        else:
            strategy_scores['TREND_FOLLOWING'] = 20
        
        # RANGE TRADING - nejlepší pro sideways trhy
        if market_type in ['low_volatility_ranging', 'mixed_conditions'] and sr['in_range']:
            strategy_scores['RANGE_TRADING'] = 85 + (5 - volatility['score']) * 5
        elif trend['strength'] == 'sideways':
            strategy_scores['RANGE_TRADING'] = 70
        else:
            strategy_scores['RANGE_TRADING'] = 30
        
        # SWING TRADING - univerzální pro střední volatilitu
        if volatility['score'] == 3 and abs(trend['score']) >= 2:
            strategy_scores['SWING_TRADING'] = 80
        elif volatility['score'] in [2, 3, 4]:
            strategy_scores['SWING_TRADING'] = 65
        else:
            strategy_scores['SWING_TRADING'] = 45
        
        # SCALPING - nejlepší pro vysokou volatilitu
        if volatility['score'] >= 4:
            strategy_scores['SCALPING'] = 85 + volatility['score'] * 3
        elif volatility['score'] >= 3:
            strategy_scores['SCALPING'] = 60
        else:
            strategy_scores['SCALPING'] = 25
        
        # DAY TRADING - univerzální pro střední podmínky
        if volatility['score'] >= 3 and abs(trend['score']) >= 1:
            strategy_scores['DAY_TRADING'] = 75
        else:
            strategy_scores['DAY_TRADING'] = 50
        
        # PRICE ACTION - nejlepší když jsou jasné patterny
        if patterns['clear_patterns']:
            strategy_scores['PRICE_ACTION'] = 80
        else:
            strategy_scores['PRICE_ACTION'] = 40
        
        # Výběr nejlepší strategie
        best_strategy = max(strategy_scores.items(), key=lambda x: x[1])
        strategy_name = best_strategy[0]
        confidence = min(100, best_strategy[1])
        
        selected_strategy = self.strategies[strategy_name].copy()
        selected_strategy['confidence'] = confidence
        selected_strategy['market_analysis'] = market_analysis
        selected_strategy['all_scores'] = strategy_scores
        
        return selected_strategy
    
    def get_strategy_parameters(self, strategy: Dict) -> Dict:
        """Získání parametrů pro vybranou strategii"""
        strategy_name = strategy['name']
        market_analysis = strategy['market_analysis']
        
        if strategy_name == 'Trend Following':
            return self._get_trend_following_params(market_analysis)
        elif strategy_name == 'Range Trading':
            return self._get_range_trading_params(market_analysis)
        elif strategy_name == 'Swing Trading':
            return self._get_swing_trading_params(market_analysis)
        elif strategy_name == 'Scalping':
            return self._get_scalping_params(market_analysis)
        elif strategy_name == 'Day Trading':
            return self._get_day_trading_params(market_analysis)
        elif strategy_name == 'Price Action':
            return self._get_price_action_params(market_analysis)
        else:
            return self._get_default_params()
    
    def _get_trend_following_params(self, analysis: Dict) -> Dict:
        """Parametry pro trend following strategii"""
        trend_strength = abs(analysis['trend']['score'])
        
        if trend_strength >= 4:
            return {
                'rsi_oversold': 35,
                'rsi_overbought': 65,
                'timeframe': '1h',
                'position_size': 0.3,
                'stop_loss': 2.0,
                'take_profit': 6.0,
                'trailing_stop': True
            }
        else:
            return {
                'rsi_oversold': 40,
                'rsi_overbought': 60,
                'timeframe': '30m',
                'position_size': 0.25,
                'stop_loss': 2.5,
                'take_profit': 5.0,
                'trailing_stop': True
            }
    
    def _get_range_trading_params(self, analysis: Dict) -> Dict:
        """Parametry pro range trading strategii"""
        return {
            'rsi_oversold': 25,
            'rsi_overbought': 75,
            'timeframe': '15m',
            'position_size': 0.2,
            'stop_loss': 1.5,
            'take_profit': 3.0,
            'use_support_resistance': True
        }
    
    def _get_scalping_params(self, analysis: Dict) -> Dict:
        """Parametry pro scalping strategii"""
        return {
            'rsi_oversold': 48,
            'rsi_overbought': 52,
            'timeframe': '1m',
            'position_size': 0.15,
            'stop_loss': 0.5,
            'take_profit': 1.0,
            'quick_exit': True
        }
    
    def _get_swing_trading_params(self, analysis: Dict) -> Dict:
        """Parametry pro swing trading strategii"""
        return {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'timeframe': '4h',
            'position_size': 0.25,
            'stop_loss': 3.0,
            'take_profit': 8.0,
            'hold_time': 'days'
        }
    
    def _get_day_trading_params(self, analysis: Dict) -> Dict:
        """Parametry pro day trading strategii"""
        return {
            'rsi_oversold': 45,
            'rsi_overbought': 55,
            'timeframe': '5m',
            'position_size': 0.25,
            'stop_loss': 2.0,
            'take_profit': 4.0,
            'close_eod': True
        }
    
    def _get_price_action_params(self, analysis: Dict) -> Dict:
        """Parametry pro price action strategii"""
        return {
            'rsi_oversold': 35,
            'rsi_overbought': 65,
            'timeframe': '15m',
            'position_size': 0.2,
            'stop_loss': 2.0,
            'take_profit': 4.0,
            'pattern_confirmation': True
        }
    
    def _get_default_params(self) -> Dict:
        """Defaultní parametry"""
        return {
            'rsi_oversold': 45,
            'rsi_overbought': 55,
            'timeframe': '5m',
            'position_size': 0.25,
            'stop_loss': 2.0,
            'take_profit': 4.0
        }

def main():
    """Test inteligentního výběru strategie"""
    print("🧠 TEST INTELIGENTNÍHO VÝBĚRU STRATEGIE")
    print("=" * 60)
    
    try:
        # Inicializace
        selector = IntelligentStrategySelector()
        exchange = ccxt.binance({'sandbox': True, 'enableRateLimit': True})
        
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        
        for symbol in symbols:
            print(f"\n📊 ANALÝZA {symbol}:")
            print("-" * 40)
            
            # Analýza trhu
            market_analysis = selector.analyze_market_conditions(exchange, symbol)
            
            if market_analysis:
                # Výběr strategie
                strategy = selector.select_optimal_strategy(market_analysis)
                
                if strategy:
                    # Parametry strategie
                    params = selector.get_strategy_parameters(strategy)
                    
                    print(f"🎯 Vybraná strategie: {strategy['name']}")
                    print(f"📝 Popis: {strategy['description']}")
                    print(f"🎲 Confidence: {strategy['confidence']:.1f}%")
                    print(f"⏰ Timeframe: {params['timeframe']}")
                    print(f"📊 RSI: {params['rsi_oversold']}/{params['rsi_overbought']}")
                    print(f"💰 Position size: {params['position_size']*100:.0f}%")
                    print(f"🛡️ SL/TP: {params['stop_loss']:.1f}%/{params['take_profit']:.1f}%")
                    
                    # Tržní podmínky
                    trend = market_analysis['trend']
                    volatility = market_analysis['volatility']
                    
                    print(f"\n📈 Tržní podmínky:")
                    print(f"   • Trend: {trend['strength']} (score: {trend['score']})")
                    print(f"   • Volatilita: {volatility['level']} (score: {volatility['score']})")
                    print(f"   • Typ trhu: {market_analysis['market_type']}")
                    
                    # Všechny skóre strategií
                    print(f"\n🏆 Skóre všech strategií:")
                    for strat_name, score in strategy['all_scores'].items():
                        emoji = "🥇" if strat_name == strategy['name'].upper().replace(' ', '_') else "📊"
                        print(f"   {emoji} {strat_name}: {score:.1f}")
        
        print(f"\n✅ Test dokončen!")
        
    except Exception as e:
        print(f"❌ Chyba testu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
