#!/usr/bin/env python3
"""
Multi-timeframe strategie
"""

from typing import Dict, List
import pandas as pd
from strategy import RSIStrategy

class MultiTimeframeStrategy:
    """Strategie používající více timeframů"""
    
    def __init__(self):
        # Různé strategie pro různé timeframy
        self.short_term = RSIStrategy(rsi_period=14, oversold=35, overbought=65)  # 15m
        self.medium_term = RSIStrategy(rsi_period=14, oversold=30, overbought=70)  # 1h
        self.long_term = RSIStrategy(rsi_period=21, oversold=25, overbought=75)   # 4h
    
    def analyze_multi_timeframe(self, symbol: str, data_15m: pd.DataFrame, 
                               data_1h: pd.DataFrame, data_4h: pd.DataFrame) -> Dict:
        """Analýza na více timeframech"""
        
        # Analýza na každém timeframu
        signal_15m = self.short_term.analyze_signal(symbol, data_15m)
        signal_1h = self.medium_term.analyze_signal(symbol, data_1h)
        signal_4h = self.long_term.analyze_signal(symbol, data_4h)
        
        # <PERSON><PERSON>hy pro různé timeframy
        weights = {
            '15m': 0.3,  # Krátký trend
            '1h': 0.4,   # Střední trend (hlavní)
            '4h': 0.3    # Dlouhý trend
        }
        
        # Skóre pro BUY/SELL
        buy_score = 0
        sell_score = 0
        
        signals = [
            ('15m', signal_15m, weights['15m']),
            ('1h', signal_1h, weights['1h']),
            ('4h', signal_4h, weights['4h'])
        ]
        
        reasons = []
        
        for timeframe, signal, weight in signals:
            if signal['action'] == 'BUY':
                buy_score += weight * (signal['confidence'] / 100)
                reasons.append(f"{timeframe}: BUY (RSI {signal['rsi']:.1f})")
            elif signal['action'] == 'SELL':
                sell_score += weight * (signal['confidence'] / 100)
                reasons.append(f"{timeframe}: SELL (RSI {signal['rsi']:.1f})")
            else:
                reasons.append(f"{timeframe}: HOLD (RSI {signal['rsi']:.1f})")
        
        # Rozhodnutí na základě skóre
        if buy_score > 0.6:  # Silný BUY signál
            action = 'BUY'
            confidence = min(100, buy_score * 100)
            reason = f"MULTI-TF BUY: {', '.join([r for r in reasons if 'BUY' in r])}"
        elif sell_score > 0.6:  # Silný SELL signál
            action = 'SELL'
            confidence = min(100, sell_score * 100)
            reason = f"MULTI-TF SELL: {', '.join([r for r in reasons if 'SELL' in r])}"
        elif buy_score > 0.4:  # Slabý BUY
            action = 'BUY'
            confidence = 50
            reason = f"WEAK MULTI-TF BUY: {reasons[1]}"  # Hlavně 1h timeframe
        else:
            action = 'HOLD'
            confidence = 0
            reason = f"MULTI-TF HOLD: {reasons[1]}"
        
        return {
            'action': action,
            'reason': reason,
            'confidence': confidence,
            'rsi': signal_1h['rsi'],  # Hlavní RSI z 1h
            'price': signal_1h['price'],
            'timeframe_signals': {
                '15m': signal_15m,
                '1h': signal_1h,
                '4h': signal_4h
            },
            'scores': {
                'buy_score': buy_score,
                'sell_score': sell_score
            }
        }
