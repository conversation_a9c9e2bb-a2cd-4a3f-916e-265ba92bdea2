#!/usr/bin/env python3
"""
PRAKTICKÝ PRŮVODCE IMPLEMENTACÍ STRATEGIÍ DO BOTA
Od teorie k funkčnímu kódu
"""

def show_implementation_methods():
    """Metody implementace strategií"""
    print("🛠️ METODY IMPLEMENTACE STRATEGIÍ DO BOTA")
    print("=" * 60)
    
    methods = {
        "1️⃣ PEVNÁ LOGIKA (IF/ELSE)": {
            "popis": "Klasické podmínky v kódu",
            "výhody": ["Rychlé", "Předvídatelné", "Snadné debugování"],
            "nevýhody": ["Neflexibilní", "Nutné přeprogramování"],
            "vhodné_pro": "Jednoduché strategie (RSI, MA crossover)",
            "příklad": """
if rsi < 30 and price > ma20:
    return 'BUY'
elif rsi > 70:
    return 'SELL'
else:
    return 'HOLD'
            """
        },
        
        "2️⃣ KONFIGURAČNÍ SOUBORY": {
            "popis": "Strategie definované v JSON/YAML",
            "výhody": ["Flexibilní", "Bez přeprogramování", "Snadné A/B testování"],
            "nevýhody": ["Složitější setup", "Omezené na předem definované funkce"],
            "vhodné_pro": "Střední složitost, více variant strategií",
            "příklad": """
{
  "strategy": "rsi_ma_combo",
  "conditions": [
    {"indicator": "rsi", "operator": "<", "value": 30},
    {"indicator": "price_vs_ma20", "operator": ">", "value": 1}
  ],
  "action": "BUY"
}
            """
        },
        
        "3️⃣ PROMPT ENGINEERING (LLM)": {
            "popis": "AI rozhoduje na základě promptu",
            "výhody": ["Velmi flexibilní", "Přirozený jazyk", "Adaptivní"],
            "nevýhody": ["Pomalé", "Nákladné", "Nepředvídatelné"],
            "vhodné_pro": "Komplexní analýzy, sentiment, news",
            "příklad": """
prompt = f'''
Tržní data: RSI={rsi}, Trend={trend}, News={news}
Doporuč akci: BUY/SELL/HOLD a důvod.
Odpověz JSON: {{"action": "...", "reason": "...", "confidence": 0-100}}
'''
            """
        },
        
        "4️⃣ MACHINE LEARNING": {
            "popis": "Model natrénovaný na historických datech",
            "výhody": ["Adaptivní", "Najde skryté vzory", "Zlepšuje se časem"],
            "nevýhody": ["Složité", "Potřeba dat", "Overfitting riziko"],
            "vhodné_pro": "Pokročilé strategie, predikce",
            "příklad": """
# Trénování modelu
model = RandomForestClassifier()
model.fit(features, labels)

# Predikce
prediction = model.predict([[rsi, macd, volume]])
            """
        }
    }
    
    for method, info in methods.items():
        print(f"\n{method}: {info['popis']}")
        print(f"✅ Výhody: {', '.join(info['výhody'])}")
        print(f"❌ Nevýhody: {', '.join(info['nevýhody'])}")
        print(f"🎯 Vhodné pro: {info['vhodné_pro']}")
        print(f"💻 Příklad:")
        print(info['příklad'])

def show_practical_examples():
    """Praktické příklady implementace"""
    print("\n💻 PRAKTICKÉ PŘÍKLADY IMPLEMENTACE")
    print("=" * 60)
    
    print("\n1️⃣ JEDNODUCHÁ RSI STRATEGIE:")
    print("-" * 40)
    print("""
def rsi_strategy(data):
    rsi = calculate_rsi(data['close'])
    price = data['close'][-1]
    ma20 = data['close'].rolling(20).mean()[-1]
    
    # Podmínky
    oversold = rsi < 30
    uptrend = price > ma20
    overbought = rsi > 70
    
    if oversold and uptrend:
        return {'action': 'BUY', 'reason': f'RSI {rsi:.1f} oversold + uptrend'}
    elif overbought:
        return {'action': 'SELL', 'reason': f'RSI {rsi:.1f} overbought'}
    else:
        return {'action': 'HOLD', 'reason': f'RSI {rsi:.1f} neutral'}
    """)
    
    print("\n2️⃣ MULTI-INDIKÁTOR STRATEGIE:")
    print("-" * 40)
    print("""
def multi_indicator_strategy(data):
    # Výpočet indikátorů
    rsi = calculate_rsi(data['close'])
    macd, macd_signal = calculate_macd(data['close'])
    bb_upper, bb_lower = calculate_bollinger_bands(data['close'])
    volume_ma = data['volume'].rolling(20).mean()[-1]
    
    price = data['close'][-1]
    volume = data['volume'][-1]
    
    # Scoring systém
    buy_score = 0
    sell_score = 0
    
    # RSI signály
    if rsi < 30:
        buy_score += 2
    elif rsi > 70:
        sell_score += 2
    
    # MACD signály
    if macd > macd_signal:
        buy_score += 1
    else:
        sell_score += 1
    
    # Bollinger Bands
    if price < bb_lower:
        buy_score += 1
    elif price > bb_upper:
        sell_score += 1
    
    # Volume confirmation
    if volume > volume_ma * 1.5:
        if buy_score > sell_score:
            buy_score += 1
        else:
            sell_score += 1
    
    # Rozhodnutí
    if buy_score >= 3:
        return {'action': 'BUY', 'confidence': min(100, buy_score * 20)}
    elif sell_score >= 3:
        return {'action': 'SELL', 'confidence': min(100, sell_score * 20)}
    else:
        return {'action': 'HOLD', 'confidence': 0}
    """)
    
    print("\n3️⃣ ADAPTIVNÍ STRATEGIE S VOLATILITOU:")
    print("-" * 40)
    print("""
def adaptive_strategy(data):
    # Výpočet volatility (ATR)
    atr = calculate_atr(data)
    volatility_percentile = get_volatility_percentile(atr)
    
    # Adaptivní parametry podle volatility
    if volatility_percentile > 80:  # Vysoká volatilita
        rsi_oversold = 40
        rsi_overbought = 60
        strategy_type = "scalping"
    elif volatility_percentile < 20:  # Nízká volatilita
        rsi_oversold = 25
        rsi_overbought = 75
        strategy_type = "range_trading"
    else:  # Střední volatilita
        rsi_oversold = 30
        rsi_overbought = 70
        strategy_type = "trend_following"
    
    # Aplikace strategie
    rsi = calculate_rsi(data['close'])
    
    if rsi <= rsi_oversold:
        return {
            'action': 'BUY',
            'strategy': strategy_type,
            'reason': f'RSI {rsi:.1f} ≤ {rsi_oversold} ({strategy_type})'
        }
    elif rsi >= rsi_overbought:
        return {
            'action': 'SELL', 
            'strategy': strategy_type,
            'reason': f'RSI {rsi:.1f} ≥ {rsi_overbought} ({strategy_type})'
        }
    else:
        return {'action': 'HOLD', 'strategy': strategy_type}
    """)

def show_prompt_engineering():
    """Prompt engineering pro LLM"""
    print("\n🤖 PROMPT ENGINEERING PRO TRADING BOTA")
    print("=" * 60)
    
    print("\n📝 ZÁKLADNÍ PROMPT TEMPLATE:")
    print("-" * 40)
    print("""
SYSTEM_PROMPT = '''
Jsi expert na technickou analýzu a trading. 
Analyzuješ tržní data a doporučuješ trading akce.
Vždy odpovídáš ve formátu JSON.
'''

USER_PROMPT = f'''
Analyzuj následující tržní data pro {symbol}:

📊 TECHNICKÉ INDIKÁTORY:
- RSI (14): {rsi:.2f}
- MACD: {macd:.4f} (Signal: {macd_signal:.4f})
- Bollinger Bands: Cena {price:.2f}, Upper {bb_upper:.2f}, Lower {bb_lower:.2f}
- Volume: {volume:,.0f} (MA20: {volume_ma:,.0f})

📈 TREND ANALÝZA:
- MA20: {ma20:.2f}
- MA50: {ma50:.2f}
- ADX: {adx:.2f}
- Trend směr: {trend_direction}

🕐 TIMEFRAME: {timeframe}
💰 AKTUÁLNÍ POZICE: {current_position}

Na základě těchto dat doporuč:
1. Akci: BUY/SELL/HOLD
2. Důvod rozhodnutí
3. Confidence (0-100%)
4. Stop Loss a Take Profit úrovně

Odpověz JSON:
{{
  "action": "BUY/SELL/HOLD",
  "reason": "detailní vysvětlení",
  "confidence": 85,
  "stop_loss": 0.98,
  "take_profit": 1.05,
  "risk_level": "low/medium/high"
}}
'''
    """)
    
    print("\n🎯 POKROČILÝ PROMPT S KONTEXTEM:")
    print("-" * 40)
    print("""
def create_advanced_prompt(market_data, news_sentiment, historical_performance):
    return f'''
    🧠 TRADING EXPERT ANALÝZA
    
    📊 AKTUÁLNÍ TRŽNÍ SITUACE:
    {format_market_data(market_data)}
    
    📰 NEWS SENTIMENT (posledních 24h):
    - Celkový sentiment: {news_sentiment['overall']}
    - Klíčové události: {news_sentiment['events']}
    - Fear & Greed Index: {news_sentiment['fear_greed']}
    
    📈 HISTORICKÁ PERFORMANCE:
    - Úspěšnost podobných signálů: {historical_performance['success_rate']}%
    - Průměrný P&L: {historical_performance['avg_pnl']}%
    - Nejlepší timeframe: {historical_performance['best_timeframe']}
    
    🎯 ÚKOL:
    Zvažuj všechny faktory a doporuč nejlepší akci.
    Mysli jako profesionální trader s 10+ lety zkušeností.
    
    Odpověz strukturovaně:
    {{
      "primary_action": "BUY/SELL/HOLD",
      "confidence": 0-100,
      "reasoning": {{
        "technical": "technická analýza",
        "fundamental": "fundamentální faktory", 
        "sentiment": "sentiment analýza",
        "risk": "risk assessment"
      }},
      "execution": {{
        "entry_price": "optimální vstupní cena",
        "stop_loss": "SL úroveň",
        "take_profit": "TP úroveň",
        "position_size": "doporučená velikost pozice %"
      }},
      "alternatives": [
        "alternativní scénáře pokud se situace změní"
      ]
    }}
    '''
    """)

def show_integration_example():
    """Příklad integrace do bota"""
    print("\n🔗 INTEGRACE DO TRADING BOTA")
    print("=" * 60)
    
    print("""
class AdvancedTradingBot:
    def __init__(self):
        self.strategies = {
            'rsi_simple': self.rsi_strategy,
            'multi_indicator': self.multi_indicator_strategy,
            'adaptive': self.adaptive_strategy,
            'llm_powered': self.llm_strategy
        }
        self.current_strategy = 'adaptive'
        
    def analyze_market(self, symbol):
        # Získání dat
        data = self.get_market_data(symbol)
        
        # Výběr strategie podle podmínek
        market_type = self.detect_market_type(data)
        self.select_optimal_strategy(market_type)
        
        # Aplikace vybrané strategie
        strategy_func = self.strategies[self.current_strategy]
        signal = strategy_func(data)
        
        return signal
    
    def detect_market_type(self, data):
        volatility = self.calculate_volatility(data)
        trend_strength = self.calculate_trend_strength(data)
        
        if volatility > 0.8 and trend_strength > 0.7:
            return 'strong_trending'
        elif volatility < 0.3:
            return 'low_volatility_range'
        elif trend_strength < 0.3:
            return 'sideways'
        else:
            return 'mixed_conditions'
    
    def select_optimal_strategy(self, market_type):
        strategy_map = {
            'strong_trending': 'adaptive',
            'low_volatility_range': 'rsi_simple', 
            'sideways': 'multi_indicator',
            'mixed_conditions': 'llm_powered'
        }
        
        new_strategy = strategy_map.get(market_type, 'adaptive')
        
        if new_strategy != self.current_strategy:
            print(f"🔄 Změna strategie: {self.current_strategy} → {new_strategy}")
            self.current_strategy = new_strategy
    """)

def main():
    """Hlavní funkce"""
    print("🛠️ PRAKTICKÝ PRŮVODCE IMPLEMENTACÍ STRATEGIÍ")
    print("=" * 70)
    
    show_implementation_methods()
    show_practical_examples()
    show_prompt_engineering()
    show_integration_example()
    
    print(f"\n🎯 DOPORUČENÝ POSTUP:")
    print(f"=" * 60)
    print(f"1️⃣ Začněte s jednoduchou IF/ELSE logikou")
    print(f"2️⃣ Přidejte konfigurační soubory pro flexibilitu")
    print(f"3️⃣ Implementujte multi-indikátor scoring")
    print(f"4️⃣ Přidejte adaptivní parametry podle volatility")
    print(f"5️⃣ Experimentujte s LLM pro komplexní analýzy")
    print(f"6️⃣ Testujte vše na historických datech")
    
    print(f"\n💡 KLÍČOVÉ RADY:")
    print(f"   • Začněte jednoduše, postupně přidávejte složitost")
    print(f"   • Vždy backtestujte před live implementací")
    print(f"   • Logujte všechna rozhodnutí pro analýzu")
    print(f"   • Mějte fallback strategii pro edge cases")
    print(f"   • Monitorujte performance a adaptujte")

if __name__ == "__main__":
    main()
