#!/usr/bin/env python3
"""
🔍 TEST P&L DEBUG VÝPISŮ
Test nových detailních debug výpisů pro P&L výpočet
"""

from colors import print_banner, success, error, highlight, warning

def show_debug_implementation():
    """Zobrazení implementace debug vý<PERSON>ů"""
    print_banner("🔍 P&L DEBUG VÝPISY IMPLEMENTOVÁNY")
    
    print(f"\n🔧 {highlight('CO JSEM PŘIDAL DO BOT.PY')}:")
    print("=" * 70)
    
    debug_features = [
        {
            'feature': 'Stav před P&L výpočtem',
            'code': 'print(f"🔍 DEBUG PŘED P&L: side={side}, symbol={symbol}")',
            'purpose': 'Ověření že se dostaneme k P&L výpočtu'
        },
        {
            'feature': 'Seznam pozic v Risk Manageru',
            'code': 'print(f"🔍 Risk Manager pozice: {list(self.risk_manager.open_positions.keys())}")',
            'purpose': 'Kontrola zda pozice existuje v risk manageru'
        },
        {
            'feature': 'Seznam pozic ve Strategy',
            'code': 'print(f"🔍 Strategy pozice: {list(self.strategy.positions.keys())}")',
            'purpose': 'Kontrola zda pozice existuje ve strategy'
        },
        {
            'feature': 'Risk Manager P&L výpočet',
            'code': 'print(f"🔍 RISK MANAGER: entry={entry_price:.4f}, exit={fill_price:.4f}, qty={quantity:.6f}")',
            'purpose': 'Zobrazení hodnot pro P&L výpočet'
        },
        {
            'feature': 'Strategy fallback',
            'code': 'print(f"🔍 STRATEGY: entry={entry_price:.4f}, exit={fill_price:.4f}, qty={quantity:.6f}")',
            'purpose': 'Fallback když risk manager selže'
        },
        {
            'feature': 'Finální P&L',
            'code': 'print(f"🔍 FINÁLNÍ P&L: {pnl}")',
            'purpose': 'Konečný výsledek P&L výpočtu'
        }
    ]
    
    for feature in debug_features:
        print(f"\n🔍 {feature['feature']}:")
        print(f"   💻 Kód: {feature['code']}")
        print(f"   🎯 Účel: {feature['purpose']}")

def show_expected_outputs():
    """Očekávané výstupy"""
    print(f"\n📊 {highlight('OČEKÁVANÉ DEBUG VÝSTUPY')}:")
    print("=" * 70)
    
    scenarios = [
        {
            'scenario': 'ÚSPĚŠNÝ P&L výpočet (Risk Manager)',
            'output': '''🔍 DEBUG PŘED P&L: side=sell, symbol=ADA/USDT
🔍 Risk Manager pozice: ['ADA/USDT']
🔍 Strategy pozice: ['ADA/USDT']
🔍 RISK MANAGER: entry=0.6169, exit=0.6200, qty=5255.020000
🔍 RISK MANAGER P&L: 16.29
🔍 FINÁLNÍ P&L: 16.29''',
            'meaning': 'Pozice existuje v Risk Manageru, P&L se počítá správně'
        },
        {
            'scenario': 'FALLBACK na Strategy',
            'output': '''🔍 DEBUG PŘED P&L: side=sell, symbol=ADA/USDT
🔍 Risk Manager pozice: []
🔍 Strategy pozice: ['ADA/USDT']
🔍 RISK MANAGER: Pozice ADA/USDT NEEXISTUJE!
🔍 STRATEGY: entry=0.6169, exit=0.6200, qty=5255.020000
🔍 STRATEGY P&L: 16.29
🔍 FINÁLNÍ P&L: 16.29''',
            'meaning': 'Risk Manager nemá pozici, ale Strategy ano - fallback funguje'
        },
        {
            'scenario': 'PROBLÉM - žádná pozice',
            'output': '''🔍 DEBUG PŘED P&L: side=sell, symbol=ADA/USDT
🔍 Risk Manager pozice: []
🔍 Strategy pozice: []
🔍 RISK MANAGER: Pozice ADA/USDT NEEXISTUJE!
🔍 STRATEGY: Pozice ADA/USDT má quantity=0!
🔍 FINÁLNÍ P&L: None''',
            'meaning': 'Žádná pozice neexistuje - tady je problém!'
        },
        {
            'scenario': 'PROBLÉM - symbol mismatch',
            'output': '''🔍 DEBUG PŘED P&L: side=sell, symbol=ADA/USDT
🔍 Risk Manager pozice: ['ADAUSDT']
🔍 Strategy pozice: ['ADAUSDT']
🔍 RISK MANAGER: Pozice ADA/USDT NEEXISTUJE!
🔍 STRATEGY: Pozice ADA/USDT má quantity=0!
🔍 FINÁLNÍ P&L: None''',
            'meaning': 'Symbol mismatch - pozice je pod jiným názvem'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📊 {highlight(scenario['scenario'])}:")
        print(f"```")
        print(scenario['output'])
        print(f"```")
        print(f"💡 {scenario['meaning']}")

def show_diagnostic_guide():
    """Diagnostický průvodce"""
    print(f"\n🩺 {highlight('DIAGNOSTICKÝ PRŮVODCE')}:")
    print("=" * 70)
    
    diagnostics = [
        {
            'symptom': 'Risk Manager pozice: []',
            'diagnosis': 'Pozice se nepřidává do Risk Manageru',
            'fix': 'Zkontrolovat add_position() volání při BUY'
        },
        {
            'symptom': 'Strategy pozice: []',
            'diagnosis': 'Pozice se nepřidává do Strategy',
            'fix': 'Zkontrolovat strategy.add_position() volání'
        },
        {
            'symptom': 'Risk Manager pozice: [\'ADAUSDT\'] ale hledáme ADA/USDT',
            'diagnosis': 'Symbol normalizace nefunguje',
            'fix': 'Opravit symbol formatting'
        },
        {
            'symptom': 'STRATEGY: Pozice má quantity=0',
            'diagnosis': 'Strategy pozice se už vymazala',
            'fix': 'Zkontrolovat pořadí operací'
        },
        {
            'symptom': 'FINÁLNÍ P&L: None',
            'diagnosis': 'P&L výpočet selhal',
            'fix': 'Opravit podle konkrétní příčiny výše'
        }
    ]
    
    for diag in diagnostics:
        print(f"\n🔍 {error('SYMPTOM')}: {diag['symptom']}")
        print(f"   📊 Diagnóza: {diag['diagnosis']}")
        print(f"   🔧 Oprava: {diag['fix']}")

def show_testing_instructions():
    """Instrukce pro testování"""
    print(f"\n🧪 {highlight('INSTRUKCE PRO TESTOVÁNÍ')}:")
    print("=" * 70)
    
    steps = [
        {
            'step': 1,
            'action': 'Spustit bota v live režimu',
            'command': 'python main.py --mode live',
            'watch': 'Sledovat konzoli pro debug výpisy'
        },
        {
            'step': 2,
            'action': 'Počkat na BUY signál',
            'command': 'Čekat na RSI ≤ 30',
            'watch': 'Pozice se přidá do Risk Manageru'
        },
        {
            'step': 3,
            'action': 'Počkat na SELL signál',
            'command': 'Čekat na RSI ≥ 55 nebo ≤ 25',
            'watch': 'Debug výpisy P&L výpočtu'
        },
        {
            'step': 4,
            'action': 'Analyzovat debug výstupy',
            'command': 'Porovnat s očekávanými výstupy',
            'watch': 'Identifikovat kde je problém'
        },
        {
            'step': 5,
            'action': 'Opravit konkrétní problém',
            'command': 'Podle diagnostického průvodce',
            'watch': 'Ověřit že oprava funguje'
        }
    ]
    
    for step in steps:
        print(f"\n🔢 {highlight(f'KROK {step["step"]}: {step["action"]}')}:")
        print(f"   💻 Příkaz: {step['command']}")
        print(f"   👀 Sledovat: {step['watch']}")

def show_quick_test():
    """Rychlý test"""
    print(f"\n⚡ {highlight('RYCHLÝ TEST BEZ ČEKÁNÍ')}:")
    print("=" * 70)
    
    print(f"🔧 {highlight('MOŽNOSTI RYCHLÉHO TESTOVÁNÍ')}:")
    print(f"   1. 📊 Demo režim s kratšími intervaly")
    print(f"   2. 🧪 Unit test s mock daty")
    print(f"   3. 📝 Manual test s fake pozicemi")
    print(f"   4. 🔍 Log analýza existujících obchodů")
    
    print(f"\n🎯 {highlight('DOPORUČENÍ')}:")
    print(f"   ✅ Spustit live bota a sledovat debug výpisy")
    print(f"   ✅ První SELL obchod ukáže kde je problém")
    print(f"   ✅ Pak můžeme rychle opravit")

def main():
    """Hlavní funkce"""
    print("🔍 TEST P&L DEBUG VÝPISŮ")
    print("=" * 70)
    
    show_debug_implementation()
    show_expected_outputs()
    show_diagnostic_guide()
    show_testing_instructions()
    show_quick_test()
    
    print(f"\n🎉 {success('DEBUG VÝPISY IMPLEMENTOVÁNY!')}")
    print(f"   🔍 Detailní debug informace o P&L výpočtu")
    print(f"   📊 Kontrola pozic v Risk Manageru i Strategy")
    print(f"   🩺 Diagnostický průvodce pro rychlou opravu")
    print(f"   ⚡ Identifikace problému při prvním SELL obchodu")
    
    print(f"\n🚀 {highlight('PŘIPRAVENO K TESTOVÁNÍ!')}")
    print(f"python main.py --mode live")
    
    print(f"\n👀 {highlight('SLEDUJTE TYTO DEBUG VÝPISY')}:")
    print(f"   🔍 DEBUG PŘED P&L: side=sell, symbol=...")
    print(f"   🔍 Risk Manager pozice: [...]")
    print(f"   🔍 Strategy pozice: [...]")
    print(f"   🔍 FINÁLNÍ P&L: ...")
    
    print(f"\n💡 {highlight('PŘI PRVNÍM SELL OBCHODU UVIDÍME PŘESNĚ CO SE DĚJE!')}")

if __name__ == "__main__":
    main()
