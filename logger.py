import logging
import os
from datetime import datetime
from config import Config

class TradingLogger:
    """Centralizovaný logging systém pro trading bota"""
    
    def __init__(self):
        self.setup_logger()
    
    def setup_logger(self):
        """Nastavení loggeru"""
        # Vytvoření logs adresáře
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # Konfigurace loggeru
        self.logger = logging.getLogger('TradingBot')
        self.logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Formát zpráv
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler(
            f'logs/{Config.LOG_FILE}', 
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # <PERSON><PERSON>id<PERSON><PERSON> handlerů
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message):
        """Info zpráva"""
        self.logger.info(message)
    
    def warning(self, message):
        """Warning zpráva"""
        self.logger.warning(message)
    
    def error(self, message):
        """Error zpráva"""
        self.logger.error(message)
    
    def debug(self, message):
        """Debug zpráva"""
        self.logger.debug(message)
    
    def trade_log(self, action, symbol, price, quantity, reason=""):
        """Speciální log pro obchody"""
        message = f"TRADE: {action} {quantity} {symbol} @ {price}"
        if reason:
            message += f" | Reason: {reason}"
        self.info(message)
    
    def balance_log(self, balance, pnl=None):
        """Log pro balance"""
        message = f"BALANCE: {balance} USDT"
        if pnl is not None:
            message += f" | PnL: {pnl:+.2f} USDT"
        self.info(message)
    
    def strategy_log(self, symbol, rsi, action, reason):
        """Log pro strategii"""
        message = f"STRATEGY: {symbol} | RSI: {rsi:.2f} | Action: {action} | {reason}"
        self.info(message)

# Globální instance loggeru
logger = TradingLogger()
