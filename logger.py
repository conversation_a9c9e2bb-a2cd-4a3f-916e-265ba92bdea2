import logging
import os
from datetime import datetime
from config import Config
from colors import (
    print_trade_open, print_trade_close, print_balance,
    print_strategy, print_cycle_start, print_cycle_end,
    print_positions, print_error, print_success, print_warning,
    success, error, warning, info
)

class TradingLogger:
    """Centralizovaný logging systém pro trading bota"""
    
    def __init__(self):
        self.setup_logger()
    
    def setup_logger(self):
        """Nastavení loggeru"""
        # Vytvoření logs adresáře
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # Konfigurace loggeru
        self.logger = logging.getLogger('TradingBot')
        self.logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Formát zpráv
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler(
            f'logs/{Config.LOG_FILE}', 
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # Přidání handlerů
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message):
        """Info zpráva s barevným výstupem"""
        self.logger.info(message)
        print(info(message))

    def warning(self, message):
        """Warning zpráva s barevným výstupem"""
        self.logger.warning(message)
        print_warning(message)

    def error(self, message):
        """Error zpráva s barevným výstupem"""
        self.logger.error(message)
        print_error(message)
    
    def debug(self, message):
        """Debug zpráva"""
        self.logger.debug(message)
    
    def trade_log(self, action, symbol, price, quantity, reason="", pnl=None):
        """Speciální log pro obchody s barevným výstupem"""
        # Log do souboru (bez barev)
        message = f"TRADE: {action} {quantity} {symbol} @ {price}"
        if reason:
            message += f" | Reason: {reason}"
        if pnl is not None:
            message += f" | P&L: {pnl:+.2f} USDT"
        self.logger.info(message)

        # Barevný výstup do konzole
        if action.upper() in ['BUY', 'OPEN']:
            print_trade_open(symbol, quantity, price, reason)
        elif action.upper() in ['SELL', 'CLOSE']:
            print_trade_close(symbol, quantity, price, pnl or 0, reason)
    
    def balance_log(self, balance, pnl=None):
        """Log pro balance s barevným výstupem"""
        # Log do souboru
        message = f"BALANCE: {balance} USDT"
        if pnl is not None:
            message += f" | PnL: {pnl:+.2f} USDT"
        self.logger.info(message)

        # Barevný výstup do konzole
        print_balance(balance, pnl)
    
    def strategy_log(self, symbol, rsi, action, reason):
        """Log pro strategii s barevným výstupem"""
        # Log do souboru
        message = f"STRATEGY: {symbol} | RSI: {rsi:.2f} | Action: {action} | {reason}"
        self.logger.info(message)

        # Barevný výstup do konzole
        print_strategy(symbol, rsi, action, reason)

# Globální instance loggeru
logger = TradingLogger()
