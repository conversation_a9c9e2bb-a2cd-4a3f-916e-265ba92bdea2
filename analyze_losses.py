#!/usr/bin/env python3
"""
🚨 KRITICKÁ ANALÝZA ZTRÁT
Analýza ztráty 4000 USDT (26.6%) - z 15000 na 11000
"""

import os
import re
from datetime import datetime
from colors import print_banner, success, error, highlight, warning

def analyze_balance_history():
    """Analýza historie balance"""
    print_banner("🚨 KRITICKÁ ANALÝZA ZTRÁT")
    
    print(f"\n💰 {error('STAV ÚČTU')}:")
    print("=" * 70)
    print(f"   📊 Počáteční balance: 15,000 USDT")
    print(f"   📊 Aktuální balance: 11,060.80 USDT")
    print(f"   📊 Ztráta: -3,939.20 USDT")
    print(f"   📊 Ztráta v %: -26.26%")
    print(f"   🚨 {error('KRITICKÁ ZTRÁTA!')}")

def identify_loss_sources():
    """Identifikace zdrojů ztrát"""
    print(f"\n🔍 {highlight('MOŽNÉ ZDROJE ZTRÁT')}:")
    print("=" * 70)
    
    loss_sources = [
        {
            'source': 'Špatná RSI strategie',
            'description': 'RSI 30/70 může být příliš agresivní',
            'impact': 'Vysoký',
            'evidence': 'Časté nákupy v downtrend'
        },
        {
            'source': 'Chybějící stop loss',
            'description': 'Pozice se neuzavírají při ztrátách',
            'impact': 'Kritický',
            'evidence': 'P&L: +0.00 USDT místo skutečných ztrát'
        },
        {
            'source': 'Špatný position sizing',
            'description': '20% na obchod je příliš mnoho',
            'impact': 'Vysoký',
            'evidence': 'Velké ztráty při špatných obchodech'
        },
        {
            'source': 'Market timing',
            'description': 'Nákupy v bear market',
            'impact': 'Vysoký',
            'evidence': 'Celkový trend dolů'
        },
        {
            'source': 'Fees a slippage',
            'description': 'Trading fees se sčítají',
            'impact': 'Střední',
            'evidence': 'Každý obchod stojí 0.1%'
        },
        {
            'source': 'Emergency sells',
            'description': 'Prodeje v panice bez zisku',
            'impact': 'Vysoký',
            'evidence': 'P&L: +0.00 USDT při emergency sell'
        },
        {
            'source': 'Chybná P&L logika',
            'description': 'Skutečné ztráty se nezobrazují',
            'impact': 'Kritický',
            'evidence': 'Všechny P&L jsou 0.00'
        }
    ]
    
    for source in loss_sources:
        impact_color = error if source['impact'] == 'Kritický' else warning if source['impact'] == 'Vysoký' else highlight
        print(f"\n🚨 {impact_color(source['source'])} ({source['impact']} dopad):")
        print(f"   📊 Popis: {source['description']}")
        print(f"   🔍 Důkaz: {source['evidence']}")

def calculate_theoretical_performance():
    """Výpočet teoretické výkonnosti"""
    print(f"\n📊 {highlight('TEORETICKÁ ANALÝZA VÝKONNOSTI')}:")
    print("=" * 70)
    
    scenarios = [
        {
            'scenario': 'Perfektní RSI strategie',
            'buy_rsi': 30,
            'sell_rsi': 70,
            'success_rate': 60,
            'avg_gain': 5,
            'avg_loss': -3,
            'trades_per_month': 10
        },
        {
            'scenario': 'Konzervativní strategie',
            'buy_rsi': 25,
            'sell_rsi': 75,
            'success_rate': 70,
            'avg_gain': 3,
            'avg_loss': -2,
            'trades_per_month': 5
        },
        {
            'scenario': 'Aktuální strategie (odhad)',
            'buy_rsi': 30,
            'sell_rsi': 60,
            'success_rate': 30,
            'avg_gain': 2,
            'avg_loss': -8,
            'trades_per_month': 15
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📊 {scenario['scenario']}:")
        
        # Výpočet měsíční výkonnosti
        successful_trades = scenario['trades_per_month'] * (scenario['success_rate'] / 100)
        losing_trades = scenario['trades_per_month'] - successful_trades
        
        monthly_gain = successful_trades * scenario['avg_gain']
        monthly_loss = losing_trades * scenario['avg_loss']
        net_monthly = monthly_gain + monthly_loss
        
        print(f"   🎯 Úspěšnost: {scenario['success_rate']}%")
        print(f"   📈 Průměrný zisk: +{scenario['avg_gain']}%")
        print(f"   📉 Průměrná ztráta: {scenario['avg_loss']}%")
        print(f"   🔄 Obchodů/měsíc: {scenario['trades_per_month']}")
        print(f"   💰 Měsíční výkonnost: {net_monthly:+.1f}%")
        
        if net_monthly < 0:
            print(f"   🚨 {error('ZTRÁTOVÁ STRATEGIE!')}")
        elif net_monthly > 5:
            print(f"   ✅ {success('VÝNOSNÁ STRATEGIE')}")
        else:
            print(f"   ⚠️ {warning('MARGINÁLNÍ STRATEGIE')}")

def analyze_risk_management_failures():
    """Analýza selhání risk managementu"""
    print(f"\n🛡️ {error('SELHÁNÍ RISK MANAGEMENTU')}:")
    print("=" * 70)
    
    failures = [
        {
            'failure': 'Žádný skutečný stop loss',
            'problem': 'P&L se nepočítá, ztráty se neomezují',
            'solution': 'Implementovat tvrdý stop loss na -3%'
        },
        {
            'failure': 'Příliš velký position size',
            'problem': '20% na obchod = 5 špatných obchodů = -100%',
            'solution': 'Snížit na 5-10% na obchod'
        },
        {
            'failure': 'Žádný portfolio stop loss',
            'problem': 'Žádná ochrana před velkými ztrátami',
            'solution': 'Stop trading při -10% celkové ztrátě'
        },
        {
            'failure': 'Chybějící trend filter',
            'problem': 'Nákupy v bear market',
            'solution': 'Přidat MA200 trend filter'
        },
        {
            'failure': 'Žádná diverzifikace',
            'problem': 'Všechny peníze v crypto',
            'solution': 'Omezit crypto exposure na 50%'
        }
    ]
    
    for failure in failures:
        print(f"\n❌ {error(failure['failure'])}:")
        print(f"   🚨 Problém: {failure['problem']}")
        print(f"   🔧 Řešení: {failure['solution']}")

def create_emergency_fixes():
    """Nouzové opravy"""
    print(f"\n🚨 {error('NOUZOVÉ OPRAVY - OKAMŽITĚ!')}:")
    print("=" * 70)
    
    emergency_fixes = [
        {
            'priority': 1,
            'fix': 'STOP TRADING okamžitě',
            'reason': 'Zabránit dalším ztrátám',
            'action': 'Zastavit bota, uzavřít všechny pozice'
        },
        {
            'priority': 2,
            'fix': 'Opravit P&L výpočet',
            'reason': 'Vidět skutečné ztráty',
            'action': 'Debug P&L logiku, opravit výpočty'
        },
        {
            'priority': 3,
            'fix': 'Implementovat tvrdý stop loss',
            'reason': 'Omezit ztráty na -3% na obchod',
            'action': 'Přidat price-based stop loss'
        },
        {
            'priority': 4,
            'fix': 'Snížit position size',
            'reason': 'Snížit riziko na obchod',
            'action': 'Změnit z 20% na 5% na obchod'
        },
        {
            'priority': 5,
            'fix': 'Přidat trend filter',
            'reason': 'Nekupovat v downtrend',
            'action': 'Implementovat MA200 filter'
        }
    ]
    
    for fix in emergency_fixes:
        priority_color = error if fix['priority'] <= 2 else warning if fix['priority'] <= 4 else highlight
        print(f"\n🚨 {priority_color(f'PRIORITA {fix["priority"]}: {fix["fix"]}')}:")
        print(f"   🎯 Důvod: {fix['reason']}")
        print(f"   🔧 Akce: {fix['action']}")

def create_new_strategy():
    """Nová konzervativní strategie"""
    print(f"\n🛡️ {success('NOVÁ KONZERVATIVNÍ STRATEGIE')}:")
    print("=" * 70)
    
    new_strategy = {
        'name': 'Konzervativní RSI s ochranou kapitálu',
        'parameters': {
            'rsi_buy': 20,  # Více oversold
            'rsi_sell': 80,  # Více overbought
            'position_size': '5%',  # Menší pozice
            'stop_loss': '-3%',  # Tvrdý stop loss
            'take_profit': '+6%',  # Vyšší take profit
            'max_positions': 3,  # Max 3 pozice současně
            'trend_filter': 'MA200',  # Pouze long v uptrend
            'portfolio_stop': '-10%'  # Stop při -10% celkové ztrátě
        },
        'rules': [
            'BUY pouze když RSI ≤ 20 A cena > MA200',
            'SELL když RSI ≥ 80 NEBO ztráta ≥ 3% NEBO zisk ≥ 6%',
            'Max 5% kapitálu na jeden obchod',
            'Max 3 otevřené pozice současně',
            'STOP TRADING při -10% celkové ztrátě',
            'Denní review všech pozic'
        ]
    }
    
    print(f"📊 {highlight(new_strategy['name'])}:")
    print(f"\n🎯 Parametry:")
    for param, value in new_strategy['parameters'].items():
        print(f"   • {param}: {value}")
    
    print(f"\n📋 Pravidla:")
    for i, rule in enumerate(new_strategy['rules'], 1):
        print(f"   {i}. {rule}")

def show_immediate_actions():
    """Okamžité akce"""
    print(f"\n⚡ {error('OKAMŽITÉ AKCE - TEĎ!')}:")
    print("=" * 70)
    
    actions = [
        {
            'action': '🛑 ZASTAVIT BOTA',
            'command': 'Ctrl+C nebo kill process',
            'urgency': 'KRITICKÉ'
        },
        {
            'action': '📊 UZAVŘÍT VŠECHNY POZICE',
            'command': 'Manual sell všech otevřených pozic',
            'urgency': 'VYSOKÉ'
        },
        {
            'action': '🔍 ANALYZOVAT SKUTEČNÉ P&L',
            'command': 'Spočítat skutečné zisky/ztráty',
            'urgency': 'VYSOKÉ'
        },
        {
            'action': '🔧 OPRAVIT P&L LOGIKU',
            'command': 'Debug a oprava P&L výpočtů',
            'urgency': 'VYSOKÉ'
        },
        {
            'action': '🛡️ IMPLEMENTOVAT NOVOU STRATEGII',
            'command': 'Konzervativní parametry',
            'urgency': 'STŘEDNÍ'
        }
    ]
    
    for action in actions:
        urgency_color = error if action['urgency'] == 'KRITICKÉ' else warning if action['urgency'] == 'VYSOKÉ' else highlight
        print(f"\n{urgency_color(action['action'])} ({action['urgency']}):")
        print(f"   💻 Příkaz: {action['command']}")

def main():
    """Hlavní funkce"""
    print("🚨 KRITICKÁ ANALÝZA ZTRÁT - ZTRÁTA 4000 USDT!")
    print("=" * 70)
    
    analyze_balance_history()
    identify_loss_sources()
    calculate_theoretical_performance()
    analyze_risk_management_failures()
    create_emergency_fixes()
    create_new_strategy()
    show_immediate_actions()
    
    print(f"\n🚨 {error('KRITICKÝ ZÁVĚR')}:")
    print("=" * 70)
    print(f"   💰 Ztráta 26.6% je NEAKCEPTOVATELNÁ")
    print(f"   🚨 Bot musí být OKAMŽITĚ zastaven")
    print(f"   🔧 P&L logika musí být opravena")
    print(f"   🛡️ Risk management musí být přepracován")
    print(f"   📊 Nová konzervativní strategie je nutná")
    
    print(f"\n⚡ {error('OKAMŽITÉ KROKY')}:")
    print(f"   1. 🛑 ZASTAVIT BOTA TEĎKA!")
    print(f"   2. 📊 UZAVŘÍT VŠECHNY POZICE")
    print(f"   3. 🔍 SPOČÍTAT SKUTEČNÉ ZTRÁTY")
    print(f"   4. 🔧 OPRAVIT P&L LOGIKU")
    print(f"   5. 🛡️ IMPLEMENTOVAT NOVOU STRATEGII")

if __name__ == "__main__":
    main()
