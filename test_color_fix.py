#!/usr/bin/env python3
"""
Test opravy barevného zobrazení - čitelnost žlutého rámečku
"""

from colors import *

def test_color_readability():
    """Test čitelnosti barev"""
    print("🎨 TEST OPRAVY BAREVNÉHO ZOBRAZENÍ")
    print("=" * 50)
    
    print("\n❌ PŘED OPRAVOU (nečitelné):")
    print("Žlutý text na žlutém pozadí:")
    # Simulace starého problému
    old_hold = colorize(" HOLD ", Colors.YELLOW, Colors.BOLD, Colors.BG_YELLOW)
    print(f"RSI: 16.26 | {old_hold} | Důvod signálu")
    print("↑ Tento text byl nečitelný!")
    
    print("\n✅ PO OPRAVĚ (čitelné):")
    print("Černý text na žlutém pozadí:")
    # Nová opravená verze
    new_hold = hold_signal(" HOLD ")
    print(f"RSI: 16.26 | {new_hold} | Důvod signálu")
    print("↑ Tento text je perfektně čitelný!")
    
    print("\n🎯 POROVNÁNÍ VŠECH SIGNÁLŮ:")
    
    # BUY signál
    buy = buy_signal(" BUY ")
    print(f"📈 BTC/USDT | RSI: 44.2 | {buy} | RSI ≤ 45 + UPTREND")
    
    # SELL signál  
    sell = sell_signal(" SELL ")
    print(f"📉 ETH/USDT | RSI: 56.8 | {sell} | RSI ≥ 55 + DOWNTREND")
    
    # HOLD signál - OPRAVENÝ
    hold = hold_signal(" HOLD ")
    print(f"⏸️ ADA/USDT | RSI: 49.1 | {hold} | RSI v neutrální zóně")
    
    print("\n🔍 DETAILNÍ TEST ČITELNOSTI:")
    
    # Test různých kombinací
    test_cases = [
        ("BUY", buy_signal(" BUY "), "Bílý text na zeleném pozadí"),
        ("SELL", sell_signal(" SELL "), "Bílý text na červeném pozadí"), 
        ("HOLD", hold_signal(" HOLD "), "Černý text na žlutém pozadí - OPRAVENO"),
    ]
    
    for signal_name, signal_colored, description in test_cases:
        print(f"   {signal_colored} - {description}")
    
    print(f"\n✅ VŠECHNY SIGNÁLY JSOU NYNÍ ČITELNÉ!")
    
    return True

def test_rsi_colors():
    """Test RSI barev"""
    print(f"\n📊 TEST RSI BAREVNÉHO FORMÁTOVÁNÍ:")
    print(f"=" * 50)
    
    # Test různých RSI hodnot
    rsi_values = [
        (42, "Pod 45 - BUY signál"),
        (58, "Nad 55 - SELL signál"),
        (49, "Neutrální zóna - HOLD"),
        (44, "Blízko oversold"),
        (56, "Blízko overbought")
    ]
    
    for rsi, description in rsi_values:
        rsi_colored = format_rsi(rsi, oversold=45, overbought=55)
        print(f"   {rsi_colored} - {description}")
    
    print(f"\n✅ RSI BARVY FUNGUJÍ SPRÁVNĚ!")

def show_before_after():
    """Ukázka před a po opravě"""
    print(f"\n🔄 PŘED vs PO OPRAVĚ:")
    print(f"=" * 50)
    
    print(f"❌ PŘED (nečitelné):")
    print(f"   08:56:41,398 - TradingBot")
    print(f"   RSI: 16.26 | {colorize(' HOLD ', Colors.YELLOW, Colors.BOLD, Colors.BG_YELLOW)} |")
    print(f"   ↑ Žlutý text na žlutém pozadí = nečitelné")
    
    print(f"\n✅ PO OPRAVĚ (čitelné):")
    print(f"   08:56:41,398 - TradingBot")
    print(f"   RSI: 16.26 | {hold_signal(' HOLD ')} |")
    print(f"   ↑ Černý text na žlutém pozadí = perfektně čitelné")
    
    print(f"\n🎯 VÝSLEDEK:")
    print(f"   • Žlutý rámeček zůstal (vizuální identifikace)")
    print(f"   • Text je nyní černý (čitelnost)")
    print(f"   • Perfektní kontrast")
    print(f"   • Profesionální vzhled")

def main():
    """Hlavní funkce"""
    success = test_color_readability()
    test_rsi_colors()
    show_before_after()
    
    if success:
        print(f"\n🎉 BAREVNÁ OPRAVA ÚSPĚŠNÁ!")
        print(f"✅ Žlutý rámeček s černým textem")
        print(f"✅ Perfektní čitelnost")
        print(f"✅ Zachována vizuální identifikace")
        print(f"✅ Profesionální vzhled")
        
        print(f"\n🚀 SPUŠTĚNÍ S OPRAVENÝM ZOBRAZENÍM:")
        print(f"python main.py --mode live")
        
    else:
        print(f"\n❌ OPRAVA SELHALA")

if __name__ == "__main__":
    main()
