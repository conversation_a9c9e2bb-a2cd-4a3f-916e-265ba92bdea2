#!/usr/bin/env python3
"""
Jednoduchý test trading bota bez pandas
O<PERSON><PERSON><PERSON><PERSON> z<PERSON>lad<PERSON>
"""

import sys
import os

def test_basic_imports():
    """Test základních importů"""
    print("🔍 Testování základních importů...")
    
    try:
        import ccxt
        print("✅ ccxt - OK")
    except ImportError:
        print("❌ ccxt - CHYBÍ")
        return False
    
    try:
        import ta
        print("✅ ta - OK")
    except ImportError:
        print("❌ ta - CHYBÍ")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv - OK")
    except ImportError:
        print("❌ python-dotenv - CHYBÍ")
        return False
    
    return True

def test_config():
    """Test konfigurace"""
    print("\n⚙️ Testování konfigurace...")
    
    try:
        # Přidání aktuálního adresáře do Python path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from config import Config
        
        print(f"✅ Burza: {Config.EXCHANGE}")
        print(f"✅ Symboly: {Config.SYMBOLS}")
        print(f"✅ Timeframe: {Config.TIMEFRAME}")
        print(f"✅ RSI parametry: {Config.RSI_PERIOD}/{Config.RSI_OVERSOLD}/{Config.RSI_OVERBOUGHT}")
        print(f"✅ Max pozice: {Config.MAX_POSITION_SIZE * 100}%")
        print(f"✅ Stop Loss: {Config.STOP_LOSS_PERCENT}%")
        print(f"✅ Take Profit: {Config.TAKE_PROFIT_PERCENT}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování konfigurace: {e}")
        return False

def test_logger():
    """Test loggeru"""
    print("\n📝 Testování loggeru...")
    
    try:
        from logger import logger
        
        logger.info("Test info zpráva")
        logger.warning("Test warning zpráva")
        
        print("✅ Logger funguje správně")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování loggeru: {e}")
        return False

def test_files_exist():
    """Test existence souborů"""
    print("\n📁 Testování existence souborů...")
    
    required_files = [
        'main.py',
        'bot.py', 
        'strategy.py',
        'risk_management.py',
        'config.py',
        'logger.py',
        'requirements.txt',
        '.env.example',
        'README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - CHYBÍ")
            missing_files.append(file)
    
    return len(missing_files) == 0

def test_env_example():
    """Test .env.example souboru"""
    print("\n🔧 Testování .env.example...")
    
    try:
        with open('.env.example', 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_vars = [
            'EXCHANGE=',
            'API_KEY=',
            'API_SECRET=',
            'SANDBOX=',
            'SYMBOLS=',
            'RSI_PERIOD=',
            'MAX_POSITION_SIZE='
        ]
        
        missing_vars = []
        for var in required_vars:
            if var in content:
                print(f"✅ {var.rstrip('=')}")
            else:
                print(f"❌ {var.rstrip('=')} - CHYBÍ")
                missing_vars.append(var)
        
        return len(missing_vars) == 0
        
    except Exception as e:
        print(f"❌ Chyba při čtení .env.example: {e}")
        return False

def main():
    """Hlavní funkce"""
    print("🤖 TRADING BOT - JEDNODUCHÝ TEST")
    print("=" * 40)
    
    tests = [
        ("Základní importy", test_basic_imports),
        ("Existence souborů", test_files_exist),
        (".env.example", test_env_example),
        ("Konfigurace", test_config),
        ("Logger", test_logger),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ Test {test_name} selhal")
        except Exception as e:
            print(f"❌ Test {test_name} selhal s chybou: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 VÝSLEDKY: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy prošly! Bot je připraven k použití.")
        print("\n📋 Další kroky:")
        print("1. Nastavte API klíče v .env souboru:")
        print("   cp .env.example .env")
        print("   # Upravte .env soubor")
        print("2. Spusťte: python main.py --mode test")
        print("3. Spusťte backtesting: python main.py --mode backtest")
        print("4. Nebo použijte interaktivní menu: python run_bot.py")
        return True
    else:
        print("❌ Některé testy selhaly.")
        if passed >= 3:
            print("💡 Základní funkčnost je v pořádku, můžete pokračovat.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
