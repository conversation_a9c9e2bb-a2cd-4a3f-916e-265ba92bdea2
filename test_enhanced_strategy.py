#!/usr/bin/env python3
"""
Test vylepšené strategie bez pandas závislostí
"""

from colors import print_banner, success, warning, highlight, profit, loss

def test_enhanced_strategy():
    """Test nové strategie"""
    print_banner("🚀 ENHANCED TRADING STRATEGY 🚀")
    
    print(highlight("✨ NOVÉ FUNKCE:"))
    print(success("1. 🎯 RSI 35/65 (místo 30/70) - více signálů"))
    print(success("2. 📈 Moving Average 20 filter - jen trendy"))
    print(success("3. 📊 MACD confirmation - momentum"))
    print(success("4. 📱 Discord notifikace - real-time alerts"))
    print(success("5. 🎨 Vylepšený barevný výstup"))
    
    print(f"\n{highlight('🧠 LOGIKA STRATEGIE:')}")
    
    print(f"\n{profit('🟢 TRIPLE BUY SIGNÁL:')}")
    print("   • RSI ≤ 35 (oversold)")
    print("   • Cena > MA20 (uptrend)")
    print("   • MACD > Signal (bullish momentum)")
    print("   • Confidence: 80-100%")
    
    print(f"\n{warning('🟡 STRONG BUY SIGNÁL:')}")
    print("   • RSI ≤ 35 + Cena > MA20")
    print("   • NEBO RSI ≤ 35 + MACD bullish")
    print("   • Confidence: 60-80%")
    
    print(f"\n{loss('🔴 TRIPLE SELL SIGNÁL:')}")
    print("   • RSI ≥ 65 (overbought)")
    print("   • Cena < MA20 (downtrend)")
    print("   • MACD < Signal (bearish momentum)")
    print("   • Confidence: 80-100%")
    
    print(f"\n{highlight('📊 OČEKÁVANÉ VÝSLEDKY:')}")
    print(success("✅ 3-5x více obchodních signálů"))
    print(success("✅ Vyšší win rate díky trend filtru"))
    print(success("✅ Lepší timing díky MACD"))
    print(success("✅ Okamžité Discord notifikace"))
    print(success("✅ Barevné rozlišení podle confidence"))
    
    print(f"\n{highlight('🎨 BAREVNÝ VÝSTUP:')}")
    print("🟢 TRIPLE BUY: BTC/USDT | RSI: 32.1 + UPTREND + MACD BULLISH")
    print("🟡 STRONG BUY: ETH/USDT | RSI: 34.5 + UPTREND")
    print("🔴 TRIPLE SELL: ADA/USDT | RSI: 67.2 + DOWNTREND + MACD BEARISH")
    print("⏸️ HOLD: BTC/USDT | RSI: 45.3 v neutrální zóně")
    
    print(f"\n{highlight('📱 DISCORD NOTIFIKACE:')}")
    print(success("🚀 NÁKUP BTC/USDT"))
    print(success("💰 Cena: $45,230.50"))
    print(success("🎯 Confidence: 85%"))
    print(success("📝 Důvod: TRIPLE BUY: RSI 32.1 + UPTREND + MACD BULLISH"))
    
    print(f"\n{highlight('⚙️ KONFIGURACE:')}")
    print(f"   • RSI Period: 14")
    print(f"   • RSI Oversold: 35 (dříve 30)")
    print(f"   • RSI Overbought: 65 (dříve 70)")
    print(f"   • Moving Average: 20")
    print(f"   • MACD: 12,26,9 (standard)")
    print(f"   • Position Size: 20%")
    print(f"   • Stop Loss: 3%")
    print(f"   • Take Profit: 5%")
    
    print(f"\n{highlight('🚀 SPUŠTĚNÍ:')}")
    print(success("python main.py --mode live"))
    print(warning("Nezapomeňte nastavit DISCORD_WEBHOOK v .env souboru!"))
    
    print(f"\n{profit('🎉 ENHANCED STRATEGY JE PŘIPRAVENA! 🎉')}")

if __name__ == "__main__":
    test_enhanced_strategy()
