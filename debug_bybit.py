#!/usr/bin/env python3
"""
Debug Bybit API - detailní analýza problému
"""

import ccxt
import sys
import os
import requests
import time
import hmac
import hashlib
from dotenv import load_dotenv

# Načtení environment variables
load_dotenv()

def check_api_key_format():
    """Kontrola formátu API klíčů"""
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    print("🔍 Analýza API klíčů...")
    print(f"API Key délka: {len(api_key)}")
    print(f"API Secret délka: {len(api_secret)}")
    print(f"API Key formát: {api_key[:4]}...{api_key[-4:]}")
    print(f"API Secret formát: {api_secret[:4]}...{api_secret[-4:]}")
    
    # Bybit API klíče obvykle mají specifický formát
    if len(api_key) < 10 or len(api_secret) < 10:
        print("❌ API klíče jsou příliš krátké")
        return False
    
    print("✅ Formát klíčů vypadá v pořádku")
    return True

def test_manual_request():
    """Manuální test Bybit API požadavku"""
    print("\n🔧 Manuální test API požadavku...")
    
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    # Testnet URL
    base_url = "https://api-testnet.bybit.com"
    endpoint = "/v5/account/wallet-balance"
    
    # Parametry
    timestamp = str(int(time.time() * 1000))
    params = f"accountType=UNIFIED&timestamp={timestamp}"
    
    # Vytvoření podpisu
    param_str = timestamp + api_key + "5000" + params
    signature = hmac.new(
        api_secret.encode('utf-8'),
        param_str.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    # Headers
    headers = {
        'X-BAPI-API-KEY': api_key,
        'X-BAPI-SIGN': signature,
        'X-BAPI-SIGN-TYPE': '2',
        'X-BAPI-TIMESTAMP': timestamp,
        'X-BAPI-RECV-WINDOW': '5000',
        'Content-Type': 'application/json'
    }
    
    try:
        url = f"{base_url}{endpoint}?{params}"
        response = requests.get(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Manuální požadavek úspěšný!")
            return True
        else:
            print("❌ Manuální požadavek selhal")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při manuálním požadavku: {e}")
        return False

def test_public_endpoints():
    """Test veřejných endpointů"""
    print("\n📊 Test veřejných endpointů...")
    
    try:
        # Test bez autentifikace
        exchange = ccxt.bybit()
        
        # Test ticker
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ BTC/USDT ticker: {ticker['last']}")
        
        # Test orderbook
        orderbook = exchange.fetch_order_book('BTC/USDT', 5)
        print(f"✅ Orderbook: bid={orderbook['bids'][0][0]}, ask={orderbook['asks'][0][0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu veřejných endpointů: {e}")
        return False

def test_different_ccxt_versions():
    """Test různých konfigurací ccxt"""
    print("\n🔄 Test různých ccxt konfigurací...")
    
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    configs = [
        {
            'name': 'Základní testnet',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'testnet': True,
            }
        },
        {
            'name': 'Sandbox mód',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': True,
            }
        },
        {
            'name': 'Explicitní URL',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'urls': {
                    'api': 'https://api-testnet.bybit.com',
                }
            }
        },
        {
            'name': 'Unified account',
            'config': {
                'apiKey': api_key,
                'secret': api_secret,
                'testnet': True,
                'options': {
                    'defaultType': 'unified',
                }
            }
        }
    ]
    
    for config_info in configs:
        print(f"\n🧪 Test: {config_info['name']}")
        try:
            exchange = ccxt.bybit(config_info['config'])
            
            # Jednoduchý test - získání account info
            balance = exchange.fetch_balance()
            print(f"✅ {config_info['name']} - úspěch!")
            print(f"   Balance keys: {list(balance.keys())}")
            return True
            
        except Exception as e:
            print(f"❌ {config_info['name']} - chyba: {e}")
    
    return False

def check_api_permissions():
    """Kontrola oprávnění API"""
    print("\n🔐 Doporučení pro API oprávnění...")
    print("Ujistěte se, že máte nastavena tato oprávnění:")
    print("✅ Read-Write")
    print("✅ Derivatives (pro futures)")
    print("✅ Spot (pro spot trading)")
    print("❌ Withdraw (NIKDY nepovolujte!)")
    print("\nTaké zkontrolujte:")
    print("- IP whitelist (pokud je nastaven)")
    print("- Expiraci API klíčů")
    print("- Že používáte testnet klíče pro testnet")

def main():
    """Hlavní funkce"""
    print("🔍 BYBIT API DEBUG")
    print("=" * 50)
    
    # Kontrola formátu klíčů
    if not check_api_key_format():
        return
    
    # Test veřejných endpointů
    test_public_endpoints()
    
    # Test různých konfigurací
    if test_different_ccxt_versions():
        print("\n🎉 Našli jsme funkční konfiguraci!")
        return
    
    # Manuální test
    test_manual_request()
    
    # Doporučení
    check_api_permissions()
    
    print("\n" + "=" * 50)
    print("📋 SHRNUTÍ:")
    print("1. Zkontrolujte oprávnění API klíčů na Bybit")
    print("2. Ujistěte se, že používáte testnet klíče")
    print("3. Zkontrolujte IP whitelist")
    print("4. Zkuste vytvořit nové API klíče")
    print("5. Mezitím můžete použít demo režim:")
    print("   python demo_mode.py")

if __name__ == "__main__":
    main()
