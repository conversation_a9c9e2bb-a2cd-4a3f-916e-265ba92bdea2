#!/usr/bin/env python3
"""
Spuštění agresivního trading bota s novými parametry
"""

import os
import sys
from dotenv import load_dotenv
from colors import print_banner, success, error, warning, highlight, profit

def show_aggressive_config():
    """Zobrazení agresivní konfigurace"""
    load_dotenv()
    
    print_banner("🚀 AGRESIVNÍ TRADING BOT KONFIGURACE 🚀")
    
    print(highlight("📊 NOVÉ AGRESIVNÍ PARAMETRY:"))
    print(success(f"   • RSI Oversold: 40 (místo 35) → VÍCE BUY signálů"))
    print(success(f"   • RSI Overbought: 60 (místo 65) → VÍCE SELL signálů"))
    print(success(f"   • Timeframe: 5m (místo 1h) → RYCHLEJŠÍ reakce"))
    print(success(f"   • Interval: 30s (místo 60s) → ČASTĚJŠÍ kontroly"))
    
    print(f"\n{highlight('🎯 OČEKÁVANÉ VÝSLEDKY:')}")
    print(profit("✅ 5-10x více trading signálů"))
    print(profit("✅ Rychlejší reakce na volatilní BTC trh"))
    print(profit("✅ Zachytí všechny dips z vašeho grafu"))
    print(profit("✅ Perfektní pro oscilace kolem 107k"))
    
    print(f"\n{highlight('🔧 AKTUÁLNÍ NASTAVENÍ:')}")
    print(f"   🔑 API Key: {os.getenv('API_KEY', 'NENÍ NASTAVENO')[:8]}...")
    print(f"   🏢 Exchange: {os.getenv('EXCHANGE', 'binance')}")
    print(f"   🧪 Sandbox: {os.getenv('SANDBOX', 'True')}")
    print(f"   📱 Discord: {'✅ Nastaveno' if os.getenv('DISCORD_WEBHOOK') else '❌ Není nastaveno'}")
    
    print(f"\n{highlight('💰 TRADING LOGIKA PRO BTC @ 107k:')}")
    print(success("🟢 BUY když RSI ≤ 40 + uptrend"))
    print(f"   → Při poklesech na 106,500-106,800")
    print(error("🔴 SELL když RSI ≥ 60 + downtrend"))
    print(f"   → Při růstech na 107,500-107,800")
    print(warning("🟡 HOLD v neutrální zóně 40-60"))
    
    return True

def check_requirements():
    """Kontrola požadavků"""
    print(f"\n{highlight('🔍 KONTROLA POŽADAVKŮ:')}")
    
    # API klíče
    api_key = os.getenv('API_KEY')
    api_secret = os.getenv('API_SECRET')
    
    if not api_key or not api_secret:
        print(error("❌ API klíče nejsou nastavené!"))
        print(warning("Nastavte API_KEY a API_SECRET v .env souboru"))
        return False
    
    print(success("✅ API klíče nastavené"))
    
    # Exchange
    exchange = os.getenv('EXCHANGE', 'binance')
    if exchange not in ['binance', 'bybit']:
        print(error(f"❌ Nepodporovaná burza: {exchange}"))
        return False
    
    print(success(f"✅ Exchange: {exchange}"))
    
    # Sandbox
    sandbox = os.getenv('SANDBOX', 'True').lower() == 'true'
    if sandbox:
        print(success("✅ Testnet režim (bezpečné)"))
    else:
        print(warning("⚠️ LIVE režim - reálné peníze!"))
    
    return True

def launch_bot():
    """Spuštění bota"""
    print(f"\n{highlight('🚀 SPOUŠTĚNÍ AGRESIVNÍHO BOTA:')}")
    
    try:
        # Import a spuštění
        from main import run_live_trading
        
        print(success("🤖 Inicializace bota..."))
        print(profit("🎯 Agresivní strategie aktivována!"))
        print(warning("📊 Sledujte výstup pro trading signály"))
        
        # Spuštění
        success_result = run_live_trading()
        
        if success_result:
            print(success("✅ Bot úspěšně dokončen"))
        else:
            print(error("❌ Bot skončil s chybou"))
            
    except KeyboardInterrupt:
        print(warning("\n⏹️ Bot zastaven uživatelem"))
    except Exception as e:
        print(error(f"❌ Chyba při spuštění: {e}"))

def main():
    """Hlavní funkce"""
    # Zobrazení konfigurace
    show_aggressive_config()
    
    # Kontrola požadavků
    if not check_requirements():
        print(f"\n{error('❌ OPRAVTE KONFIGURACI A ZKUSTE ZNOVU!')}")
        return
    
    # Potvrzení spuštění
    print(f"\n{highlight('⚠️ POTVRZENÍ SPUŠTĚNÍ:')}")
    print("🤖 Spustíte AGRESIVNÍ trading bota")
    print("📊 Bot bude obchodovat s RSI 40/60")
    print("⏰ Kontroly každých 30 sekund")
    print("🧪 Testnet režim (bezpečné)")
    
    response = input(f"\n{highlight('❓ Pokračovat? (ano/ne): ')}")
    
    if response.lower() in ['ano', 'yes', 'y']:
        launch_bot()
    else:
        print(warning("✅ Spuštění zrušeno"))

if __name__ == "__main__":
    main()
